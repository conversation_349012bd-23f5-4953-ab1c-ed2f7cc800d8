{
  // 继承根目录的设置，但针对Vue文件进行优化
  "editor.formatOnSave": true,
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": "explicit",
    "source.fixAll.stylelint": "explicit",
    "source.organizeImports": "never"
  },

  // Vue文件特殊配置
  "[vue]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode",
    "editor.formatOnSave": true,
    "editor.codeActionsOnSave": {
      "source.fixAll.eslint": "explicit"
    }
  },

  // ESLint配置
  "eslint.validate": ["javascript", "typescript", "vue"],
  "eslint.workingDirectories": ["."],

  // Prettier配置
  "prettier.configPath": "./.prettierrc.mjs",
  "prettier.requireConfig": true,

  // Vue语言服务配置
  "vue.server.hybridMode": true,
  "vue.format.enable": true,
  "vue.autoInsert.dotValue": true,
  "vue.autoInsert.bracketSpacing": true,

  // 文件关联
  "files.associations": {
    "*.vue": "vue"
  }
}
