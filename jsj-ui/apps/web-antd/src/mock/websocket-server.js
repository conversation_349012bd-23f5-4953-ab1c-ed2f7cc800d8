const WebSocket = require('ws');

// 模拟凭证数据
const mockVouchers = [
  {
    id: 1,
    type: '记',
    record_date: '2025-01-15',
    details: [
      {
        summary:
          '购买办公用品,购买办公用品,购买办公用品,购买办公用品,购买办公用品',
        account: '管理费用-办公费',
        debit: 1200,
        credit: 0,
      },
      {
        summary: '银行存款',
        account: '银行存款-工商银行',
        debit: 0,
        credit: 1200,
      },
    ],
    executor: 'people',
    reviewed: false,
    source_type: '进项发票',
    source_info: {
      invoice_info: {
        fund_desc: '办公用品（笔、纸张、文件夹等）',
        amount: 1061.95,
        tax: 138.05,
        total: 1200,
        id: '25922000000026476226',
      },
    },
  },
  {
    id: 2,
    type: '借',
    record_date: '2025-01-16',
    details: [
      {
        summary: '销售商品',
        account: '银行存款-建设银行',
        debit: 11_300,
        credit: 0,
      },
      {
        summary: '主营业务收入',
        account: '主营业务收入-产品销售',
        debit: 0,
        credit: 10_000,
      },
      {
        summary: '应交税费-增值税',
        account: '应交税费-应交增值税-销项税额',
        debit: 0,
        credit: 1300,
      },
    ],
    executor: 'llm',
    reviewed: true,
    source_type: '销项发票',
    source_info: {
      invoice_info: {
        fund_desc: '软件产品销售',
        amount: 10_000,
        tax: 1300,
        total: 11_300,
        id: 20_250_002,
      },
    },
  },
  {
    id: 3,
    type: '转',
    record_date: '2025-01-20',
    details: [
      {
        summary: '银行转账手续费',
        account: '财务费用-手续费',
        debit: 25.5,
        credit: 0,
      },
      {
        summary: '银行存款减少',
        account: '银行存款-工商银行',
        debit: 0,
        credit: 25.5,
      },
    ],
    executor: 'history',
    reviewed: false,
    source_type: '银行回单',
    source_info: {
      bank_receipt_info: {
        total_income_amount: 150_000,
        income_transaction_num: 12,
        total_expense_amount: 85_000,
        expense_transaction_num: 8,
        months: ['202501'],
      },
    },
  },
  {
    id: 4,
    type: '记',
    record_date: '2025-01-25',
    details: [
      {
        summary: '计提工资',
        account: '管理费用-工资',
        debit: 45_000,
        credit: 0,
      },
      {
        summary: '应付职工薪酬',
        account: '应付职工薪酬-工资',
        debit: 0,
        credit: 35_000,
      },
      {
        summary: '应付职工薪酬-社保',
        account: '应付职工薪酬-社会保险费',
        debit: 0,
        credit: 7000,
      },
      {
        summary: '应付职工薪酬-公积金',
        account: '应付职工薪酬-住房公积金',
        debit: 0,
        credit: 3000,
      },
    ],
    executor: 'llm',
    reviewed: true,
    source_type: '工资单',
    source_info: {
      payroll_info: {
        total_gross_salary: 45_000,
        total_employer_contributions: 10_000,
        total_employee_deductions: 8000,
        months: ['202501'],
      },
    },
  },
  {
    id: 5,
    type: '借',
    record_date: '2025-01-28',
    details: [
      {
        summary: '采购原材料',
        account: '原材料-钢材',
        debit: 23_400,
        credit: 0,
      },
      {
        summary: '应交税费-进项税',
        account: '应交税费-应交增值税-进项税额',
        debit: 3042,
        credit: 0,
      },
      {
        summary: '应付账款',
        account: '应付账款-供应商A',
        debit: 0,
        credit: 26_442,
      },
    ],
    executor: 'people',
    reviewed: false,
    source_type: '进项发票',
    source_info: {
      invoice_info: {
        fund_desc: '优质钢材（Q235B）',
        amount: 23_400,
        tax: 3042,
        total: 26_442,
        id: 20_250_003,
      },
    },
  },
  {
    id: 6,
    type: '结',
    record_date: '2025-01-31',
    details: [
      {
        summary: '月末结转成本',
        account: '主营业务成本',
        debit: 18_000,
        credit: 0,
      },
      {
        summary: '库存商品减少',
        account: '库存商品',
        debit: 0,
        credit: 18_000,
      },
    ],
    executor: 'llm',
    reviewed: true,
    source_type: '银行回单',
    source_info: {
      bank_receipt_info: {
        total_income_amount: 280_000,
        income_transaction_num: 25,
        total_expense_amount: 195_000,
        expense_transaction_num: 18,
        months: ['202501', '202502'],
      },
    },
  },
];

// 创建WebSocket服务器
const wss = new WebSocket.Server({ port: 8080 });

console.log('WebSocket服务器启动在端口 8080');

wss.on('connection', (ws) => {
  console.log('\n=== 客户端已连接 ===');
  console.log('当前时间:', new Date().toLocaleString());
  console.log('可用凭证数量:', mockVouchers.length);

  // 发送欢迎消息
  ws.send(
    JSON.stringify({
      type: 'connected',
      message: '已连接到凭证管理系统',
    }),
  );

  console.log('已发送连接确认消息');

  ws.on('message', (message) => {
    try {
      const data = JSON.parse(message);
      console.log('收到消息:', data);

      switch (data.type) {
        case 'delete_voucher': {
          // 删除凭证
          const deleteIndex = mockVouchers.findIndex((v) => v.id === data.id);
          if (deleteIndex !== -1) {
            mockVouchers.splice(deleteIndex, 1);
            ws.send(
              JSON.stringify({
                type: 'voucher_deleted',
                id: data.id,
              }),
            );
            console.log('凭证已删除:', data.id);
          }
          break;
        }

        case 'get_source_detail': {
          console.log('📄 获取原始数据详情:', data.voucher_id);
          const voucher = mockVouchers.find((v) => v.id === data.voucher_id);
          if (voucher) {
            const detailData = generateSourceDetail(voucher);
            // 模拟网络延迟
            setTimeout(() => {
              ws.send(
                JSON.stringify({
                  type: 'source_detail',
                  voucher_id: data.voucher_id,
                  detail: detailData,
                }),
              );
              console.log('✅ 详情数据已发送');
            }, 800); // 0.8秒延迟
          }
          break;
        }

        case 'get_vouchers': {
          // 返回所有凭证
          console.log('📋 返回凭证列表，共', mockVouchers.length, '条凭证');
          ws.send(
            JSON.stringify({
              type: 'vouchers_list',
              vouchers: mockVouchers,
            }),
          );
          console.log('✅ 凭证列表已发送');
          break;
        }

        case 'update_voucher': {
          // 更新凭证
          const voucherIndex = mockVouchers.findIndex(
            (v) => v.id === data.voucher.id,
          );
          if (voucherIndex !== -1) {
            mockVouchers[voucherIndex] = { ...data.voucher };
            ws.send(
              JSON.stringify({
                type: 'voucher_updated',
                voucher: mockVouchers[voucherIndex],
              }),
            );
            console.log('凭证已更新:', data.voucher.id);
          }
          break;
        }

        default: {
          console.log('未知消息类型:', data.type);
        }
      }
    } catch (error) {
      console.error('处理消息时出错:', error);
      ws.send(
        JSON.stringify({
          type: 'error',
          message: '服务器处理消息时出错',
        }),
      );
    }
  });

  ws.on('close', () => {
    console.log('客户端已断开连接');
  });

  ws.on('error', (err) => {
    console.error('WebSocket错误:', err);
  });

  // 模拟定期推送新凭证
  const interval = setInterval(() => {
    if (
      ws.readyState === WebSocket.OPEN && // 随机生成新凭证（演示用）
      Math.random() < 0.1
    ) {
      // 10% 概率生成新凭证
      const newVoucher = {
        id: Date.now(),
        type: ['记', '借', '转', '结'][Math.floor(Math.random() * 4)],
        record_date: new Date().toISOString().split('T')[0],
        details: [
          {
            summary: '自动生成的测试凭证',
            account: '测试科目',
            debit: Math.random() * 10_000,
            credit: 0,
          },
        ],
        executor: 'llm',
        reviewed: false,
        source_type: ['进项发票', '销项发票', '银行回单', '工资单'][
          Math.floor(Math.random() * 4)
        ],
        source_info: {
          invoice_info: {
            fund_desc: '自动生成的测试数据',
            amount: Math.random() * 10_000,
            tax: Math.random() * 1000,
            total: Math.random() * 11_000,
            id: Date.now(),
          },
        },
      };

      mockVouchers.push(newVoucher);
      ws.send(
        JSON.stringify({
          type: 'voucher_added',
          voucher: newVoucher,
        }),
      );
      console.log('推送新凭证:', newVoucher.id);
    }
  }, 30_000); // 每30秒检查一次

  // 清理定时器
  ws.on('close', () => {
    clearInterval(interval);
  });
});

// 生成原始数据详情
function generateSourceDetail(voucher) {
  const { source_type, source_info } = voucher;

  switch (source_type) {
    case '工资单': {
      return {
        payroll_detail: {
          company_name: '我司名称',
          months: source_info.payroll_info?.months || ['202501'],
          employee_count: 15,
          employees: [
            {
              name: '张三',
              department: '技术部',
              position: '高级工程师',
              basic_salary: 8000,
              bonus: 2000,
              gross_salary: 10_000,
              employee_social_insurance: 800,
              employee_housing_fund: 500,
              personal_income_tax: 290,
              net_salary: 8410,
            },
            {
              name: '李四',
              department: '销售部',
              position: '销售经理',
              basic_salary: 7000,
              bonus: 1500,
              gross_salary: 8500,
              employee_social_insurance: 680,
              employee_housing_fund: 425,
              personal_income_tax: 195,
              net_salary: 7200,
            },
            {
              name: '王五',
              department: '财务部',
              position: '会计',
              basic_salary: 6000,
              bonus: 1000,
              gross_salary: 7000,
              employee_social_insurance: 560,
              employee_housing_fund: 350,
              personal_income_tax: 145,
              net_salary: 5945,
            },
            {
              name: '赵六',
              department: '人事部',
              position: '人事专员',
              basic_salary: 5500,
              bonus: 800,
              gross_salary: 6300,
              employee_social_insurance: 504,
              employee_housing_fund: 315,
              personal_income_tax: 118,
              net_salary: 5363,
            },
            {
              name: '孙七',
              department: '技术部',
              position: '初级工程师',
              basic_salary: 5000,
              bonus: 600,
              gross_salary: 5600,
              employee_social_insurance: 448,
              employee_housing_fund: 280,
              personal_income_tax: 92,
              net_salary: 4780,
            },
          ],
          total_gross_salary:
            source_info.payroll_info?.total_gross_salary || 45_000,
          total_employee_deductions:
            source_info.payroll_info?.total_employee_deductions || 8000,
          total_employer_contributions:
            source_info.payroll_info?.total_employer_contributions || 10_000,
          total_net_salary: 32_698,
        },
      };
    }
    case '进项发票':

    case '销项发票': {
      return {
        invoice_detail: {
          invoice_code: '144001900111',
          invoice_number: '23456789',
          invoice_date: '2025-01-15',
          seller_name:
            source_type === '进项发票' ? '北京科技有限公司' : '我司名称',
          seller_tax_id:
            source_type === '进项发票'
              ? '91110000123456789X'
              : '91110000987654321A',
          buyer_name: source_type === '进项发票' ? '我司名称' : '客户公司名称',
          buyer_tax_id:
            source_type === '进项发票'
              ? '91110000987654321A'
              : '91110000123456789X',
          items: [
            {
              name: source_type === '进项发票' ? '办公用品' : '软件产品',
              specification:
                source_type === '进项发票' ? 'A4纸、签字笔等' : 'V1.0',
              unit: source_type === '进项发票' ? '套' : '套',
              quantity: source_type === '进项发票' ? 10 : 1,
              unit_price: source_type === '进项发票' ? 106.195 : 10_000,
              amount: source_info.invoice_info?.amount || 1000,
              tax_rate: 13,
              tax_amount: source_info.invoice_info?.tax || 130,
            },
            {
              name: source_type === '进项发票' ? '文件夹' : '技术服务',
              specification:
                source_type === '进项发票' ? '塑料材质' : '技术支持',
              unit: source_type === '进项发票' ? '个' : '项',
              quantity: source_type === '进项发票' ? 20 : 1,
              unit_price: source_type === '进项发票' ? 15.5 : 5000,
              amount: 310,
              tax_rate: 13,
              tax_amount: 40.3,
            },
          ],
          total_amount: source_info.invoice_info?.amount || 1000,
          total_tax: source_info.invoice_info?.tax || 130,
          total_with_tax: source_info.invoice_info?.total || 1130,
        },
      };
    }

    case '银行回单': {
      return {
        bank_detail: {
          bank_name: '中国工商银行',
          account_name: '我司账户',
          account_number: '6222021234567890123',
          months: source_info.bank_receipt_info?.months || ['202501'],
          income_transactions: [
            {
              date: '2025-01-15',
              time: '14:30:25',
              counterpart_account: '****************',
              counterpart_name: '客户A公司',
              amount: 50_000,
              summary: '货款收入',
            },
            {
              date: '2025-01-18',
              time: '09:15:42',
              counterpart_account: '****************',
              counterpart_name: '客户B公司',
              amount: 75_000,
              summary: '服务费收入',
            },
            {
              date: '2025-01-22',
              time: '16:45:18',
              counterpart_account: '****************',
              counterpart_name: '客户C公司',
              amount: 25_000,
              summary: '咨询费收入',
            },
          ],
          expense_transactions: [
            {
              date: '2025-01-16',
              time: '10:20:15',
              counterpart_account: '****************',
              counterpart_name: '供应商A',
              amount: 30_000,
              summary: '采购付款',
            },
            {
              date: '2025-01-20',
              time: '15:30:28',
              counterpart_account: '****************',
              counterpart_name: '供应商B',
              amount: 25_000,
              summary: '设备采购',
            },
            {
              date: '2025-01-25',
              time: '11:45:33',
              counterpart_account: '****************',
              counterpart_name: '物业公司',
              amount: 8000,
              summary: '办公室租金',
            },
          ],
          total_income:
            source_info.bank_receipt_info?.total_income_amount || 150_000,
          income_count:
            source_info.bank_receipt_info?.income_transaction_num || 12,
          total_expense:
            source_info.bank_receipt_info?.total_expense_amount || 85_000,
          expense_count:
            source_info.bank_receipt_info?.expense_transaction_num || 8,
        },
      };
    }

    default: {
      return null;
    }
  }
}

// 优雅关闭
process.on('SIGINT', () => {
  console.log('正在关闭WebSocket服务器...');
  wss.close(() => {
    console.log('WebSocket服务器已关闭');
    process.exit(0);
  });
});
