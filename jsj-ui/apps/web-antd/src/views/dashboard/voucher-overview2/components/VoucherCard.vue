<template>
  <div class="voucher-card-container">
    <!-- 原始数据信息区域 -->
    <div class="invoice-section" :class="getSourceTypeClass(voucher.source_type)">
      <div class="invoice-header">
        <div class="invoice-title-section">
          <h2 class="invoice-title">{{ voucher.source_type }}</h2>
          <span v-if="voucher.source_info.invoice_info" class="invoice-number">[{{ voucher.source_info.invoice_info.id }}]</span>
        </div>
        <a-button type="text" size="small" @click="handleViewDetails" class="details-btn">
          详情 <RightOutlined />
        </a-button>
      </div>
      <div class="invoice-content">
        <template v-if="voucher.source_info.invoice_info">
          <div class="invoice-desc">
            {{ voucher.source_info.invoice_info.fund_desc || '-' }}
          </div>
          <div class="invoice-amounts">
            金额/税: {{ formatInvoiceAmount(voucher.source_info.invoice_info.amount) }}/{{ formatInvoiceAmount(voucher.source_info.invoice_info.tax) }} &nbsp;&nbsp; 
            合计: {{ formatInvoiceAmount(voucher.source_info.invoice_info.total) }}
          </div>
        </template>

        <template v-else-if="voucher.source_info.bank_receipt_info">
          <div class="invoice-desc">
            {{ voucher.source_info.bank_receipt_info.months.join(', ') }}
          </div>
          <div class="invoice-amounts">
            收入: ¥{{ formatNumber(voucher.source_info.bank_receipt_info.total_income_amount) }}/{{ voucher.source_info.bank_receipt_info.income_transaction_num }}笔 &nbsp;&nbsp;
            支出: ¥{{ formatNumber(voucher.source_info.bank_receipt_info.total_expense_amount) }}/{{ voucher.source_info.bank_receipt_info.expense_transaction_num }}笔
          </div>
        </template>

        <template v-else-if="voucher.source_info.payroll_info">
          <div class="invoice-desc">
            {{ voucher.source_info.payroll_info.months.join(', ') }}
          </div>
          <div class="invoice-amounts">
            税前工资: ¥{{ formatNumber(voucher.source_info.payroll_info.total_gross_salary) }} &nbsp;&nbsp;
            社保公积金: ¥{{ formatNumber(voucher.source_info.payroll_info.total_employer_contributions) }}
          </div>
        </template>
      </div>
    </div>

    <!-- 凭证信息区域 -->
    <div class="voucher-section">
      <div class="voucher-header">
        <!-- 左侧：凭证标题、AI标识、凭证号 -->
        <div class="voucher-left">
          <h2 class="voucher-title">凭证</h2>
          <span class="executor-badge" :class="getExecutorClass(voucher.executor)">
            {{ getExecutorText(voucher.executor) }}
          </span>
          <span class="voucher-number">凭证号: {{ formatDateWithoutDash(localVoucher.record_date) }}</span>
        </div>

        <!-- 右侧：添加和保存按钮 -->
        <div class="voucher-right">
          <a-button type="text" size="small" @click="addDetail" class="action-btn">
            <PlusOutlined /> 添加
          </a-button>
          <a-button type="text" size="small" @click="handleSave" class="action-btn">
            <SaveOutlined /> 保存
          </a-button>
        </div>
      </div>

      <!-- 凭证明细表格 -->
      <div class="voucher-table-container">
        <table class="voucher-table">
          <thead>
            <tr>
              <th class="col-account">科目</th>
              <th class="col-debit">借方</th>
              <th class="col-credit">贷方</th>
              <th class="col-action"></th>
            </tr>
          </thead>
          <tbody>
            <tr 
              v-for="(detail, index) in localVoucher.details" 
              :key="index"
              class="detail-row"
            >
              <td class="account-cell">
                <div class="account-content">
                  <div class="account-name">
                    <a-input 
                      v-if="editingAccount === index"
                      v-model:value="detail.account" 
                      size="small"
                      placeholder="请输入科目"
                      @blur="editingAccount = null"
                      @pressEnter="editingAccount = null"
                      @change="handleFieldChange"
                    />
                    <span v-else class="account-text editable-field" @click="editAccount(index)">{{ detail.account || '点击编辑科目' }}</span>
                  </div>
                  <div class="summary-name">
                    <a-input 
                      v-if="editingSummary === index"
                      v-model:value="detail.summary" 
                      size="small"
                      placeholder="请输入摘要"
                      @blur="editingSummary = null"
                      @pressEnter="editingSummary = null"
                      @change="handleFieldChange"
                    />
                    <span 
                      v-else-if="detail.summary && detail.summary.length > 0"
                      class="summary-text summary-ellipsis editable-field"
                      :class="{ 'summary-truncated': isSummaryTruncated(detail.summary) }"
                      :title="detail.summary"
                      @mouseenter="showTooltip($event, detail.summary)"
                      @mouseleave="hideTooltip"
                      @click="editSummary(index)"
                    >
                      ({{ getTruncatedSummary(detail.summary) }})
                    </span>
                    <span v-else class="summary-text editable-field" @click="editSummary(index)">(点击编辑摘要)</span>
                  </div>
                </div>
              </td>
              <td class="amount-cell debit-cell">
                <div class="amount-content">
                  <a-input-number 
                    v-if="editingAmount === index && editingAmountType === 'debit'"
                    v-model:value="detail.debit"
                    size="small"
                    :precision="2"
                    :min="0"
                    placeholder="借方金额"
                    :formatter="value => value ? `¥${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',') : ''"
                    :parser="value => value.replace(/¥\s?|(,*)/g, '')"
                    @blur="handleAmountBlur"
                    @pressEnter="handleAmountBlur"
                    @change="handleFieldChange"
                  />
                  <span v-else class="amount-text debit editable-field" @click="editAmount(index, 'debit')">
                    {{ detail.debit > 0 ? '¥' + formatNumber(detail.debit) : '' }}
                  </span>
                </div>
              </td>
              <td class="amount-cell credit-cell">
                <div class="amount-content">
                  <a-input-number 
                    v-if="editingAmount === index && editingAmountType === 'credit'"
                    v-model:value="detail.credit"
                    size="small"
                    :precision="2"
                    :min="0"
                    placeholder="贷方金额"
                    :formatter="value => value ? `¥${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',') : ''"
                    :parser="value => value.replace(/¥\s?|(,*)/g, '')"
                    @blur="handleAmountBlur"
                    @pressEnter="handleAmountBlur"
                    @change="handleFieldChange"
                  />
                  <span v-else class="amount-text credit editable-field" @click="editAmount(index, 'credit')">
                    {{ detail.credit > 0 ? '¥' + formatNumber(detail.credit) : '' }}
                  </span>
                </div>
              </td>
              <td class="action-cell">
                <div class="action-buttons">
                  <a-button 
                    type="text" 
                    size="small" 
                    danger 
                    @click="removeDetail(index)"
                    class="delete-btn"
                  >
                    <DeleteOutlined />
                  </a-button>
                </div>
              </td>
            </tr>
            
            <!-- 合计行 -->
            <tr class="total-row">
              <td class="total-label">
                <span class="total-text">合计</span>
              </td>
              <td class="total-debit">
                <span class="total-amount debit">¥{{ formatNumber(getTotalDebit()) }}</span>
              </td>
              <td class="total-credit">
                <span class="total-amount credit">¥{{ formatNumber(getTotalCredit()) }}</span>
              </td>
              <td class="total-action"></td>
            </tr>
          </tbody>
        </table>
      </div>


    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { message } from 'ant-design-vue';
import { RightOutlined, PlusOutlined, DeleteOutlined, SaveOutlined, EditOutlined } from '@ant-design/icons-vue';
import dayjs, { type Dayjs } from 'dayjs';
import type { Voucher, VoucherDetail } from '../types';

interface Props {
  voucher: Voucher;
}

interface Emits {
  (e: 'save', voucher: Voucher): void;
  (e: 'delete', id: number): void;
  (e: 'view-details', voucher: Voucher): void;
  (e: 'review-change', voucher: Voucher): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 本地凭证数据
const localVoucher = ref<Voucher>({ ...props.voucher });

// 编辑状态
const editingAccount = ref<number | null>(null);
const editingAmount = ref<number | null>(null);
const editingAmountType = ref<'debit' | 'credit' | null>(null);
const editingSummary = ref<number | null>(null);
const editingRow = ref<number | null>(null);

// 监听props变化
watch(() => props.voucher, (newVoucher) => {
  localVoucher.value = { ...newVoucher };
}, { deep: true });

// 格式化数字
function formatNumber(num: number): string {
  return num.toLocaleString('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 });
}

// 格式化发票金额
function formatInvoiceAmount(amount: number | null | undefined): string {
  if (amount === null || amount === undefined || amount === 0) {
    return '-';
  }
  return '¥' + formatNumber(amount);
}

// 格式化日期（去掉中横线）
function formatDateWithoutDash(dateStr: string): string {
  return dateStr.replace(/-/g, '');
}

// 获取原始数据类型对应的CSS类
function getSourceTypeClass(sourceType: string): string {
  const classMap: Record<string, string> = {
    '进项发票': 'invoice-in',
    '销项发票': 'invoice-out',
    '银行回单': 'bank-receipt',
    '工资单': 'payroll'
  };
  return classMap[sourceType] || 'invoice-out';
}

// 获取执行者文本
function getExecutorText(executor: string): string {
  const textMap: Record<string, string> = {
    'people': '人工',
    'history': '历史',
    'llm': 'AI'
  };
  return textMap[executor] || executor;
}

function getExecutorClass(executor: string): string {
  const classMap: Record<string, string> = {
    'people': 'executor-people',
    'history': 'executor-history',
    'llm': 'executor-llm'
  };
  return classMap[executor] || 'executor-llm';
}

// 添加明细
function addDetail() {
  const newDetail = {
    summary: '',
    account: '',
    debit: 0,
    credit: 0
  };
  localVoucher.value.details.push(newDetail);
  // 自动进入科目编辑模式
  const newIndex = localVoucher.value.details.length - 1;
  editingAccount.value = newIndex;
}

// 删除明细
function removeDetail(index: number) {
  localVoucher.value.details.splice(index, 1);
}

// 处理字段变化
function handleFieldChange() {
  // 字段变化时的处理逻辑
}

// 处理审核状态变化
function handleReviewChange() {
  emit('review-change', localVoucher.value);
}

// 保存
function handleSave() {
  // 验证数据
  if (!localVoucher.value.details.length) {
    message.error('请至少添加一条明细');
    return;
  }

  // 验证借贷平衡
  const totalDebit = getTotalDebit();
  const totalCredit = getTotalCredit();
  
  if (Math.abs(totalDebit - totalCredit) > 0.01) {
    message.error('借贷不平衡，请检查明细');
    return;
  }

  emit('save', localVoucher.value);
  message.success('保存成功');
}

// 删除
function handleDelete() {
  emit('delete', localVoucher.value.id);
}

// 查看详情
function handleViewDetails() {
  emit('view-details', localVoucher.value);
}

// 编辑科目
function editAccount(index: number) {
  editingAccount.value = index;
}

// 编辑金额
function editAmount(index: number, type: 'debit' | 'credit') {
  editingAmount.value = index;
  editingAmountType.value = type;
}

// 编辑摘要
function editSummary(index: number) {
  editingSummary.value = index;
}

// 处理金额失焦
function handleAmountBlur() {
  editingAmount.value = null;
  editingAmountType.value = null;
}

// 编辑整行
function editRow(index: number) {
  editingRow.value = index;
  // 保存原始数据用于取消编辑
  originalRowData.value = { ...localVoucher.value.details[index] };
}

// 保存行编辑
function saveRowEdit(index: number) {
  editingRow.value = null;
  originalRowData.value = null;
  handleFieldChange();
}

// 取消行编辑
function cancelRowEdit(index: number) {
  if (originalRowData.value) {
    localVoucher.value.details[index] = { ...originalRowData.value };
  }
  editingRow.value = null;
  originalRowData.value = null;
}

// 原始行数据（用于取消编辑）
const originalRowData = ref<VoucherDetail | null>(null);

// 计算借方总额
function getTotalDebit(): number {
  return localVoucher.value.details.reduce((sum, detail) => sum + (detail.debit || 0), 0);
}

// 计算贷方总额
function getTotalCredit(): number {
  return localVoucher.value.details.reduce((sum, detail) => sum + (detail.credit || 0), 0);
}

// 判断摘要是否需要截断
function isSummaryTruncated(summary: string): boolean {
  if (!summary) return false;
  // 假设科目列宽度的80%大约可以显示20个中文字符
  return summary.length > 20;
}

// 获取截断后的摘要
function getTruncatedSummary(summary: string): string {
  if (!summary) return '';
  if (summary.length <= 20) return summary;
  return summary.substring(0, 18) + '...';
}

// 显示自定义tooltip
function showTooltip(event: MouseEvent, content: string) {
  if (!isSummaryTruncated(content)) return;
  
  // 移除已存在的tooltip
  hideTooltip();
  
  const tooltip = document.createElement('div');
  tooltip.className = 'custom-tooltip';
  tooltip.textContent = content;
  tooltip.style.cssText = `
    position: fixed;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 12px;
    max-width: 300px;
    word-wrap: break-word;
    z-index: 9999;
    pointer-events: none;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  `;
  
  document.body.appendChild(tooltip);
  
  // 计算位置
  const rect = (event.target as HTMLElement).getBoundingClientRect();
  const tooltipRect = tooltip.getBoundingClientRect();
  
  let left = rect.left;
  let top = rect.bottom + 8;
  
  // 防止tooltip超出屏幕右边界
  if (left + tooltipRect.width > window.innerWidth) {
    left = window.innerWidth - tooltipRect.width - 10;
  }
  
  // 防止tooltip超出屏幕下边界
  if (top + tooltipRect.height > window.innerHeight) {
    top = rect.top - tooltipRect.height - 8;
  }
  
  tooltip.style.left = left + 'px';
  tooltip.style.top = top + 'px';
}

// 隐藏自定义tooltip
function hideTooltip() {
  const existingTooltip = document.querySelector('.custom-tooltip');
  if (existingTooltip) {
    existingTooltip.remove();
  }
}
</script>

<style scoped>
.voucher-card-container {
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  margin-bottom: 16px;
  width: 100%;
  max-width: 100%;
}

/* 原始数据区域样式 */
.invoice-section {
  color: white;
  padding: 16px;
  border-radius: 8px 8px 0 0;
}

.invoice-section.invoice-in {
  background: linear-gradient(135deg, #4f46e5 0%, #3b82f6 100%);
}

.invoice-section.invoice-out {
  background: linear-gradient(135deg, #4f46e5 0%, #3b82f6 100%);
}

.invoice-section.bank-receipt {
  background: linear-gradient(135deg, #f59e0b 0%, #f97316 100%);
}

.invoice-section.payroll {
  background: linear-gradient(135deg, #8b5cf6 0%, #a855f7 100%);
}

.invoice-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.invoice-title-section {
  display: flex;
  align-items: center;
  gap: 8px;
}

.invoice-title {
  font-size: 20px;
  font-weight: 600;
  margin: 0;
  color: white;
}

.invoice-number {
  font-size: 14px;
  font-weight: 400;
  color: rgba(255, 255, 255, 0.8);
}

.details-btn {
  color: white !important;
  border: 1px solid rgba(255, 255, 255, 0.3) !important;
  background: rgba(255, 255, 255, 0.1) !important;
  border-radius: 20px;
  padding: 4px 12px;
  font-size: 12px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.details-btn:hover {
  background: rgba(255, 255, 255, 0.2) !important;
}

.invoice-content {
  font-size: 14px;
}

.invoice-desc {
  margin-bottom: 8px;
  font-weight: 500;
}

.invoice-amounts {
  font-size: 14px;
  line-height: 1.4;
}

/* 凭证区域样式 */
.voucher-section {
  padding: 16px;
}

.voucher-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid #e5e7eb;
}

.voucher-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.voucher-title {
  font-size: 20px;
  font-weight: 600;
  margin: 0;
  color: #374151;
}

.executor-badge {
  font-size: 12px;
  font-weight: 600;
  padding: 4px 8px;
  border-radius: 4px;
  min-width: 24px;
  text-align: center;
}

.executor-llm {
  background: #fef3c7;
  color: #d97706;
}

.executor-people {
  background: #dcfce7;
  color: #16a34a;
}

.executor-history {
  background: #dbeafe;
  color: #2563eb;
}

.voucher-number {
  font-size: 14px;
  color: #6b7280;
}

.voucher-right {
  display: flex;
  align-items: center;
  gap: 8px;
}

.action-btn {
  color: #3b82f6 !important;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.action-btn:hover {
  color: #1d4ed8 !important;
}

/* 表格样式 */
.voucher-table-container {
  margin-bottom: 16px;
  overflow: visible;
}

.voucher-table {
  width: 100%;
  table-layout: fixed;
  border-collapse: collapse;
  background: white;
  border-radius: 6px;
  overflow: hidden;
}

.voucher-table thead {
  background: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
}

.voucher-table th {
  padding: 8px 12px;
  font-size: 14px;
  font-weight: 500;
  color: #374151;
  word-wrap: break-word;
}

.col-account {
  text-align: left;
  width: 45%;
}

.col-debit {
  text-align: right;
  width: 20%;
  padding-right: 20px;
}

.col-credit {
  text-align: right;
  width: 20%;
  padding-right: 12px;
}

.col-action {
  text-align: right;
  width: 60px;
}

.detail-row {
  border-bottom: 1px solid #f3f4f6;
  transition: background-color 0.2s;
}

.detail-row:last-of-type {
  border-bottom: none;
}

.detail-row:hover {
  background: #f9fafb;
}

.voucher-table td {
  padding: 8px 12px;
  vertical-align: top;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

.account-cell {
  text-align: left;
}

.account-content {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.account-name {
  cursor: pointer;
}

.account-text {
  font-weight: 500;
  color: #111827;
  font-size: 14px;
}

.summary-name {
  cursor: pointer;
}

.summary-text {
  font-size: 12px;
  color: #6b7280;
}

.summary-ellipsis {
  display: inline-block;
  max-width: 80%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  vertical-align: top;
}

.summary-truncated {
  border-bottom: 1px dotted #3b82f6;
  cursor: help;
  transition: all 0.2s ease;
}

.summary-truncated:hover {
  color: #3b82f6;
  background-color: rgba(59, 130, 246, 0.05);
  border-radius: 2px;
  padding: 1px 2px;
}

.amount-cell {
  text-align: right;
}

.credit-cell {
  padding-right: 4px;
}

.amount-content {
  cursor: pointer;
}

.amount-text {
  font-weight: 600;
  font-size: 14px;
}

.amount-text.debit {
  color: #059669;
}

.amount-text.credit {
  color: #dc2626;
}

.action-cell {
  text-align: right;
}

.delete-btn {
  color: #ef4444 !important;
  padding: 4px;
  border-radius: 50%;
}

.delete-btn:hover {
  background: #fef2f2 !important;
  color: #dc2626 !important;
}

.action-buttons {
  display: flex;
  gap: 4px;
  justify-content: flex-end;
}

.edit-btn {
  color: #3b82f6 !important;
  padding: 4px;
  border-radius: 50%;
}

.edit-btn:hover {
  background: #eff6ff !important;
  color: #1d4ed8 !important;
}

.save-btn {
  color: #059669 !important;
  padding: 4px;
  border-radius: 50%;
}

.save-btn:hover {
  background: #ecfdf5 !important;
  color: #047857 !important;
}

.cancel-btn {
  color: #6b7280 !important;
  padding: 4px;
  border-radius: 50%;
  font-size: 16px;
  font-weight: bold;
}

.cancel-btn:hover {
  background: #f3f4f6 !important;
  color: #374151 !important;
}

/* 合计行样式 */
.total-row {
  background: #eff6ff;
  border-top: 2px solid #3b82f6;
  font-weight: bold;
}

.total-row td {
  padding: 12px 16px;
}

.total-label {
  text-align: left;
}

.total-text {
  font-weight: bold;
  color: #111827;
}

.total-debit,
.total-credit {
  text-align: right;
}

.total-amount {
  font-weight: bold;
  font-size: 14px;
}

.total-amount.debit {
  color: #047857;
}

.total-amount.credit {
  color: #b91c1c;
}

.total-action {
  text-align: center;
}



/* 可编辑字段样式 */
.editable-field {
  transition: all 0.2s ease;
  border-radius: 4px;
  padding: 2px 4px;
  margin: -2px -4px;
}

.editable-field:hover {
  background-color: rgba(59, 130, 246, 0.1);
  border: 1px dashed #3b82f6;
  cursor: pointer;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .voucher-header {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }
  
  .voucher-left,
  .voucher-right {
    justify-content: center;
  }
  
  .voucher-footer {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }
  
  .footer-left,
  .footer-right {
    justify-content: center;
  }
  
  .voucher-table-container {
    overflow-x: auto;
  }
  
  .voucher-table {
    min-width: 600px;
  }
}
</style>
