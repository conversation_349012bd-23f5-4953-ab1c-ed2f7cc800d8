import { ref, onUnmounted } from 'vue';
import type { WebSocketConfig } from '../types';

export function useWebSocket(config: WebSocketConfig) {
  const ws = ref<WebSocket | null>(null);
  const isConnected = ref(false);
  const reconnectAttempts = ref(0);
  const maxReconnectAttempts = 5;
  const reconnectInterval = 3000;

  // 支持环境变量配置WebSocket地址
  const defaultUrl = import.meta.env.VITE_VOUCHER_WS_URL || 'ws://localhost:8080';
  const url = config.url || defaultUrl;

  function connect() {
    try {
      ws.value = new WebSocket(url);

      ws.value.onopen = () => {
        isConnected.value = true;
        reconnectAttempts.value = 0;
        console.log('WebSocket connected');
        config.onOpen?.();
      };

      ws.value.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);
          config.onMessage(data);
        } catch (error) {
          console.error('Failed to parse WebSocket message:', error);
        }
      };

      ws.value.onclose = () => {
        isConnected.value = false;
        console.log('WebSocket disconnected');
        config.onClose?.();
        
        // 自动重连
        if (reconnectAttempts.value < maxReconnectAttempts) {
          setTimeout(() => {
            reconnectAttempts.value++;
            console.log(`Attempting to reconnect... (${reconnectAttempts.value}/${maxReconnectAttempts})`);
            connect();
          }, reconnectInterval);
        }
      };

      ws.value.onerror = (error) => {
        console.error('WebSocket error:', error);
        config.onError?.(error);
      };
    } catch (error) {
      console.error('Failed to create WebSocket connection:', error);
      config.onError?.(error as Event);
    }
  }

  function sendMessage(message: any) {
    if (ws.value && isConnected.value) {
      try {
        ws.value.send(JSON.stringify(message));
      } catch (error) {
        console.error('Failed to send WebSocket message:', error);
      }
    } else {
      console.warn('WebSocket is not connected');
    }
  }

  function disconnect() {
    if (ws.value) {
      ws.value.close();
      ws.value = null;
    }
  }

  // 初始化连接
  connect();

  // 组件卸载时断开连接
  onUnmounted(() => {
    disconnect();
  });

  return {
    isConnected,
    sendMessage,
    disconnect,
    reconnect: connect
  };
}
