<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useRoute } from 'vue-router';

interface TableColumn {
  key: string;
  title: string;
  dataIndex: string;
}

interface TableData {
  title: string;
  columns: TableColumn[];
  data: Record<string, any>[];
}

const route = useRoute();
const tableData = ref<TableData | null>(null);
const loading = ref(false);

onMounted(() => {
  // 从路由参数获取数据
  const data = route.query.data;
  if (data) {
    try {
      tableData.value = JSON.parse(data as string);
    } catch (error) {
      console.error('解析表格数据失败:', error);
      loadDefaultData();
    }
  } else {
    loadDefaultData();
  }
});

function loadDefaultData() {
  tableData.value = {
    title: 'AI 数据分析结果',
    columns: [
      { key: 'id', title: 'ID', dataIndex: 'id' },
      { key: 'name', title: '名称', dataIndex: 'name' },
      { key: 'type', title: '类型', dataIndex: 'type' },
      { key: 'value', title: '数值', dataIndex: 'value' },
      { key: 'status', title: '状态', dataIndex: 'status' },
      { key: 'createTime', title: '创建时间', dataIndex: 'createTime' }
    ],
    data: Array.from({ length: 15 }, (_, index) => ({
      id: index + 1,
      name: `数据项 ${index + 1}`,
      type: ['类型A', '类型B', '类型C'][index % 3],
      value: Math.floor(Math.random() * 1000),
      status: ['正常', '异常', '待处理'][index % 3],
      createTime: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toLocaleDateString()
    }))
  };
}

function getStatusColor(status: string) {
  switch (status) {
    case '正常':
      return 'success';
    case '异常':
      return 'error';
    case '待处理':
      return 'warning';
    default:
      return 'default';
  }
}

function exportData() {
  if (!tableData.value) return;
  
  const csvContent = [
    tableData.value.columns.map(col => col.title).join(','),
    ...tableData.value.data.map(row => 
      tableData.value!.columns.map(col => row[col.dataIndex]).join(',')
    )
  ].join('\n');
  
  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
  const link = document.createElement('a');
  link.href = URL.createObjectURL(blob);
  link.download = `${tableData.value.title}.csv`;
  link.click();
}
</script>

<template>
  <div class="p-6">
    <div class="mb-6">
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-2xl font-bold text-foreground">
            {{ tableData?.title || 'AI 数据分析' }}
          </h1>
          <p class="text-muted-foreground mt-1">
            AI 智能分析生成的数据表格，支持导出和进一步分析
          </p>
        </div>
        <div class="flex gap-2">
          <button
            class="px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors"
            @click="exportData"
          >
            导出数据
          </button>
          <button
            class="px-4 py-2 border border-border rounded-md hover:bg-accent transition-colors"
            @click="loadDefaultData"
          >
            重新生成
          </button>
        </div>
      </div>
    </div>

    <div class="bg-card rounded-lg border border-border overflow-hidden">
      <div class="p-4 border-b border-border bg-muted/50">
        <div class="flex items-center justify-between">
          <div class="flex items-center gap-2">
            <div class="w-3 h-3 bg-green-500 rounded-full"></div>
            <span class="text-sm font-medium">数据已就绪</span>
          </div>
          <div class="text-sm text-muted-foreground">
            共 {{ tableData?.data.length || 0 }} 条记录
          </div>
        </div>
      </div>

      <div class="overflow-x-auto">
        <table class="w-full">
          <thead class="bg-muted/30">
            <tr>
              <th
                v-for="column in tableData?.columns"
                :key="column.key"
                class="px-4 py-3 text-left text-sm font-medium text-foreground border-b border-border"
              >
                {{ column.title }}
              </th>
            </tr>
          </thead>
          <tbody>
            <tr
              v-for="(row, index) in tableData?.data"
              :key="index"
              class="hover:bg-muted/50 transition-colors"
            >
              <td
                v-for="column in tableData?.columns"
                :key="column.key"
                class="px-4 py-3 text-sm border-b border-border"
              >
                <span
                  v-if="column.dataIndex === 'status'"
                  :class="{
                    'px-2 py-1 rounded-full text-xs font-medium': true,
                    'bg-green-100 text-green-800': row[column.dataIndex] === '正常',
                    'bg-red-100 text-red-800': row[column.dataIndex] === '异常',
                    'bg-yellow-100 text-yellow-800': row[column.dataIndex] === '待处理'
                  }"
                >
                  {{ row[column.dataIndex] }}
                </span>
                <span v-else>{{ row[column.dataIndex] }}</span>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <div class="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
      <div class="flex items-start gap-3">
        <div class="w-5 h-5 text-blue-600 mt-0.5">
          <svg fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
          </svg>
        </div>
        <div>
          <h3 class="text-sm font-medium text-blue-900">AI 分析说明</h3>
          <p class="text-sm text-blue-700 mt-1">
            此表格数据由 AI 智能分析生成。您可以通过 AI 助手上传文件进行分析，或使用快捷操作按钮快速生成不同类型的数据表格。
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.table-container {
  max-height: 600px;
  overflow-y: auto;
}

.table-container::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.table-container::-webkit-scrollbar-track {
  background: transparent;
}

.table-container::-webkit-scrollbar-thumb {
  background-color: hsl(var(--muted-foreground));
  border-radius: 3px;
}

.table-container::-webkit-scrollbar-thumb:hover {
  background-color: hsl(var(--foreground));
}
</style>
