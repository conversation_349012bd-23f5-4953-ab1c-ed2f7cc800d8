<script setup lang="ts">
import { ref, watch } from 'vue';
import { Card, Button, Alert, Space, Descriptions, Tag } from 'ant-design-vue';
import { Page } from '@vben/common-ui';
import { useCompanySelection } from '#/components/ai-chat/composables/useCompanySelection';

const {
  companyList,
  isInitializing,
  initializationError,
  selectedCompany,
  selectedMonth,
  fetchCompanyNames,
} = useCompanySelection();

const logs = ref<string[]>([]);

const addLog = (message: string) => {
  logs.value.push(`[${new Date().toLocaleTimeString()}] ${message}`);
  console.log(message);
};

// 监听状态变化
watch(isInitializing, (newVal, oldVal) => {
  addLog(`🔄 isInitializing: ${oldVal} -> ${newVal}`);
});

watch(initializationError, (newVal, oldVal) => {
  if (newVal !== oldVal) {
    addLog(`❌ initializationError: ${oldVal} -> ${newVal}`);
  }
});

watch(companyList, (newVal) => {
  addLog(`📋 companyList 更新: 数量 ${newVal.length}`);
  if (newVal.length > 0) {
    addLog(`   公司列表: ${newVal.map(c => c.name).join(', ')}`);
  }
}, { deep: true });

watch(selectedCompany, (newVal, oldVal) => {
  if (newVal !== oldVal) {
    addLog(`🏢 selectedCompany: ${oldVal} -> ${newVal}`);
  }
});

const testInitialization = async () => {
  addLog('🚀 开始测试初始化...');
  logs.value = [];
  
  try {
    await fetchCompanyNames();
    addLog('✅ fetchCompanyNames 完成');
  } catch (error) {
    addLog(`❌ fetchCompanyNames 失败: ${error}`);
  }
};

const clearLogs = () => {
  logs.value = [];
};

// 页面加载时自动开始测试
testInitialization();
</script>

<template>
  <Page
    title="初始化问题诊断"
    description="诊断 AI Chat 组件初始化卡住的问题"
  >
    <Space direction="vertical" style="width: 100%" :size="16">
      <!-- 当前状态 -->
      <Card title="当前状态">
        <Alert
          :message="isInitializing ? '正在初始化...' : initializationError ? '初始化失败' : '初始化完成'"
          :type="isInitializing ? 'info' : initializationError ? 'error' : 'success'"
          show-icon
        />
        
        <Descriptions style="margin-top: 16px;" bordered size="small">
          <Descriptions.Item label="初始化状态">
            <Tag :color="isInitializing ? 'processing' : initializationError ? 'error' : 'success'">
              {{ isInitializing ? '进行中' : initializationError ? '失败' : '完成' }}
            </Tag>
          </Descriptions.Item>
          <Descriptions.Item label="公司数量">
            {{ companyList.length }}
          </Descriptions.Item>
          <Descriptions.Item label="选中公司">
            {{ selectedCompany || '未选择' }}
          </Descriptions.Item>
          <Descriptions.Item label="选中月份">
            {{ selectedMonth || '未选择' }}
          </Descriptions.Item>
          <Descriptions.Item label="错误信息" span="2">
            {{ initializationError || '无' }}
          </Descriptions.Item>
        </Descriptions>
      </Card>

      <!-- 操作按钮 -->
      <Card title="操作">
        <Space>
          <Button 
            type="primary" 
            :loading="isInitializing"
            @click="testInitialization"
          >
            重新测试初始化
          </Button>
          <Button @click="clearLogs">
            清空日志
          </Button>
        </Space>
      </Card>

      <!-- 公司列表 -->
      <Card title="公司列表" v-if="companyList.length > 0">
        <div style="max-height: 200px; overflow-y: auto;">
          <div 
            v-for="(company, index) in companyList" 
            :key="company.id"
            style="padding: 8px; border-bottom: 1px solid #f0f0f0;"
          >
            <strong>{{ index + 1 }}.</strong> {{ company.name }}
            <span style="color: #666; margin-left: 8px;">(ID: {{ company.id }})</span>
          </div>
        </div>
      </Card>

      <!-- 实时日志 -->
      <Card title="实时日志">
        <div 
          style="
            height: 300px; 
            overflow-y: auto; 
            background: #f5f5f5; 
            padding: 12px; 
            border-radius: 4px;
            font-family: monospace;
            font-size: 13px;
          "
        >
          <div v-if="logs.length === 0" style="text-align: center; color: #999; padding: 20px;">
            暂无日志信息
          </div>
          <div v-else>
            <div 
              v-for="(log, index) in logs" 
              :key="index"
              style="margin-bottom: 4px; line-height: 1.4;"
            >
              {{ log }}
            </div>
          </div>
        </div>
      </Card>

      <!-- 诊断建议 -->
      <Card title="诊断建议">
        <div style="line-height: 1.6;">
          <h4>如果初始化一直卡住：</h4>
          <ol>
            <li><strong>检查网络请求</strong>：打开浏览器开发者工具 → Network 标签，查看是否有失败的请求</li>
            <li><strong>检查控制台错误</strong>：查看 Console 标签是否有 JavaScript 错误</li>
            <li><strong>检查 API 响应</strong>：确认 <code>/prod-api/tool/company/names</code> 接口返回正确数据</li>
            <li><strong>检查认证状态</strong>：确保用户已登录且 token 有效</li>
          </ol>

          <h4>常见问题：</h4>
          <ul>
            <li><strong>API 超时</strong>：网络慢或服务器响应慢</li>
            <li><strong>认证失败</strong>：token 过期或无效</li>
            <li><strong>CORS 问题</strong>：跨域请求被阻止</li>
            <li><strong>数据格式错误</strong>：API 返回的数据格式不符合预期</li>
          </ul>

          <Alert
            message="调试提示"
            description="如果问题持续存在，请将上方的日志信息和浏览器开发者工具的错误信息提供给开发团队"
            type="info"
            show-icon
            style="margin-top: 16px;"
          />
        </div>
      </Card>
    </Space>
  </Page>
</template>
