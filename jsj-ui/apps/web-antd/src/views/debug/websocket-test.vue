<script setup lang="ts">
import { ref } from 'vue';
import { <PERSON>ton, Card, Alert, Space, Input, Textarea } from 'ant-design-vue';
import { Page } from '@vben/common-ui';
import { safeWebSocketMessageHandler, createWebSocketMessageStats } from '#/utils/websocket-message-filter';

const testResults = ref<string[]>([]);
const stats = createWebSocketMessageStats();

const addResult = (message: string) => {
  testResults.value.push(`[${new Date().toLocaleTimeString()}] ${message}`);
};

// 测试各种消息类型
const testMessages = [
  undefined,
  null,
  '',
  'undefined',
  '   ',
  '{"type": "valid", "message": "Hello"}',
  '{"type": "ping"}',
  'ping',
  'pong',
  'invalid json {',
  '{"valid": "json"}',
  new ArrayBuffer(8),
];

const runTests = () => {
  testResults.value = [];
  stats.reset();
  
  addResult('开始测试 WebSocket 消息过滤器...');
  
  testMessages.forEach((message, index) => {
    addResult(`\n测试消息 ${index + 1}: ${typeof message === 'object' ? '[Object/ArrayBuffer]' : JSON.stringify(message)}`);
    
    stats.addMessage(message);
    
    safeWebSocketMessageHandler(
      message,
      (data) => {
        addResult(`✅ 处理成功: ${JSON.stringify(data)}`);
      },
      {
        logInvalidMessages: false,
        logHeartbeat: false,
        onError: (error, rawData) => {
          addResult(`❌ 处理失败: ${error}`);
        },
      }
    );
  });
  
  const finalStats = stats.getStats();
  addResult('\n📊 统计结果:');
  addResult(`总消息数: ${finalStats.total}`);
  addResult(`有效消息: ${finalStats.valid}`);
  addResult(`无效消息: ${finalStats.invalid}`);
  addResult(`心跳消息: ${finalStats.heartbeat}`);
  addResult(`JSON 消息: ${finalStats.json}`);
  addResult(`文本消息: ${finalStats.text}`);
  addResult(`二进制消息: ${finalStats.binary}`);
  
  if (finalStats.errors.length > 0) {
    addResult('\n❌ 错误详情:');
    finalStats.errors.forEach((error, index) => {
      addResult(`${index + 1}. ${error.error}`);
    });
  }
};

const clearResults = () => {
  testResults.value = [];
};
</script>

<template>
  <Page
    title="WebSocket 消息过滤器测试"
    description="测试 WebSocket 消息过滤和验证功能"
  >
    <Card title="消息过滤器测试">
      <Space direction="vertical" style="width: 100%">
        <Alert
          message="测试说明"
          description="此测试将验证 WebSocket 消息过滤器是否能正确处理各种类型的消息，包括 undefined、null、空字符串、无效 JSON 等"
          type="info"
          show-icon
        />
        
        <Space>
          <Button type="primary" @click="runTests">
            运行测试
          </Button>
          <Button @click="clearResults">
            清空结果
          </Button>
        </Space>
        
        <Card title="测试结果" size="small">
          <div 
            style="
              max-height: 400px; 
              overflow-y: auto; 
              background: #f5f5f5; 
              padding: 12px; 
              border-radius: 4px;
              font-family: monospace;
              font-size: 12px;
              white-space: pre-wrap;
            "
          >
            <div v-if="testResults.length === 0" style="text-align: center; color: #999;">
              点击"运行测试"开始测试
            </div>
            <div v-else>
              <div v-for="(result, index) in testResults" :key="index">
                {{ result }}
              </div>
            </div>
          </div>
        </Card>
        
        <Card title="使用示例" size="small">
          <div style="font-size: 14px; line-height: 1.6;">
            <h4>在 WebSocket 消息处理中使用：</h4>
            <pre style="background: #f5f5f5; padding: 12px; border-radius: 4px; font-size: 12px;">
// 替换原来的代码：
// const response = JSON.parse(event.data);
// handleWebSocketMessage(response, scrollToBottom);

// 使用安全的消息处理器：
safeWebSocketMessageHandler(
  event.data,
  (data) => {
    console.log('处理有效的 WebSocket 消息:', data);
    handleWebSocketMessage(data, scrollToBottom);
  },
  {
    logInvalidMessages: true,
    logHeartbeat: false,
    onError: (error, rawData) => {
      console.error(`WebSocket 消息处理错误: ${error}`, rawData);
    },
  }
);
            </pre>
            
            <h4>主要功能：</h4>
            <ul>
              <li>✅ 自动过滤 undefined、null、空字符串</li>
              <li>✅ 安全的 JSON 解析，避免解析错误</li>
              <li>✅ 自动识别和过滤心跳消息</li>
              <li>✅ 详细的错误日志和统计</li>
              <li>✅ 支持二进制数据处理</li>
            </ul>
          </div>
        </Card>
      </Space>
    </Card>
  </Page>
</template>
