<script setup lang="ts">
import { onMounted, onUnmounted, reactive, ref, watch } from 'vue';

import { SvgJisuanqi } from '@vben/icons';

import Icon, { CloseOutlined, PlusOutlined } from '@ant-design/icons-vue';
import { message } from 'ant-design-vue';

import { generatorBusinesstype, getServiceTypeSearch, getServiceTypeSubjectId } from '#/api/account-book/bookkeeping/index';
import Data from '#/common/data';
import { useAbstractData, useSubjectData } from '#/hooks/account-book/voucher/index';
import { useGlobalLoading } from '#/hooks/useGlobalLoading';
import { VNodes } from '#/views/components/index';

import emitter from '../../enter/usermitt';
import SetBusinessPop from '../SetBusinessPop/index.vue';

const emits = defineEmits(['close']);
const selectOpenRef = ref<boolean>(false);
const useSubject = useSubjectData();
const serverLists = ref<any[]>([]);
const selectValue = ref<any>();
const inputValue = ref<string>('');
const setBusinessShow = ref<boolean>(false);
const downboxBoor = {
    x: 20,
    y: 100,
};
const boxBoor = ref<any>({ x: downboxBoor.x, y: downboxBoor.y });
const inputDisable = ref<boolean>(true);
const moveDom = ref<any>(null);
let isMouseDown = false;
const downBoor = {
    x: 0,
    y: 0,
};
const addForeignClick = () => {
    // 让下拉选择框收起来
    selectOpenRef.value = false;
    setBusinessShow.value = true;
};
const useLoading = useGlobalLoading();
// select 下拉框的展开关闭的回调
const dropdownVisibleChange = (open: boolean) => {
    selectOpenRef.value = open;
};
const getlist = () => {
    getServiceTypeSearch().then((res: any) => {
        if (res.returnCode === '200') {
            Data.voucherTypes = structuredClone(res.data);
            serverLists.value = res.data;
        } else {
            message.warning(res.returnMsg);
        }
    });
};
onMounted(() => {
    // 获取数据
    if (Data.voucherTypes.length > 0) {
        serverLists.value = structuredClone(Data.voucherTypes);
    } else {
        getlist();
    }
    //
});
const mousemove = (e: any): any => {
    if (isMouseDown) {
        const x = e.clientX - downBoor.x + downboxBoor.x;
        const y = e.clientY - downBoor.y + downboxBoor.y;
        boxBoor.value = {
            x,
            y,
        };
    }
};
const mouseup = (e: any): any => {
    isMouseDown = false;
};
onMounted(() => {
    document.body.addEventListener('mousemove', mousemove);
    document.body.addEventListener('mouseup', mouseup);
});
onUnmounted(() => {
    document.body.removeEventListener('mousemove', mousemove);
    document.body.removeEventListener('mouseup', mouseup);
});
const handleMouseDown = (e: any) => {
    isMouseDown = true;
    downBoor.x = e.clientX;
    downBoor.y = e.clientY;
    downboxBoor.x = boxBoor.value.x;
    downboxBoor.y = boxBoor.value.y;
};
const selectChange = (value: string, data: any) => {
    let creditSubjectData: any;
    let debitSubjectData: any;
    let taxSubjectData: any;
    // 通过接口根据data.creditSubject data.debitSubject data.taxSubject 查询到科目id再从科目列表取出来数据
    inputDisable.value = false;
    const proms = [getServiceTypeSubjectId(data.creditSubject), getServiceTypeSubjectId(data.debitSubject)];
    if (data.taxSubject) {
        proms.push(getServiceTypeSubjectId(data.taxSubject));
    }
    Promise.all(proms).then((results) => {
        const creditSubjectid: any = results[0].data.id || data.creditSubject;
        const debitSubjectid: any = results[1].data.id || data.debitSubject;
        let taxSubjectid: any;
        if (data.taxSubject) {
            taxSubjectid = results[2].data.id || data.taxSubject;
        }
        useSubject.selectdata.value.map((v: any) => {
            if (v.id === creditSubjectid) {
                creditSubjectData = JSON.parse(JSON.stringify(v));
            }
            if (v.id === debitSubjectid) {
                debitSubjectData = JSON.parse(JSON.stringify(v));
            }
            if (data.taxSubject && v.id === taxSubjectid) {
                taxSubjectData = JSON.parse(JSON.stringify(v));
            }
        });
        emitter.emit('quick_edit_set_voucher', {
            serverType: data,
            creditSubjectData,
            debitSubjectData,
            taxSubjectData,
        });
    });
};
// 预制业务类型
const prefabricateClick = () => {
    useLoading.setShow(true);
    generatorBusinesstype()
        .then((res: any) => {
            if (res.returnCode === '200') {
                message.success(res.returnMsg);
            } else {
                message.warning(res.returnMsg);
            }
            useLoading.setShow(false);
        })
        .catch(() => {
            useLoading.setShow(false);
        });
};
</script>
<template>
    <Teleport to="body">
        <div
            class="cont"
            :style="{ left: `${boxBoor.x}px`, top: `${boxBoor.y}px` }"
        >
            <div
                class="txt"
                ref="moveDom"
                @mousedown="handleMouseDown"
            >
                快速录入凭证
            </div>
            <div
                class="close"
                @click="emits('close')"
            >
                <CloseOutlined />
            </div>
            <div class="box1">
                <a-select
                    v-model:value="selectValue"
                    size="small"
                    style="width: 100%"
                    :open="selectOpenRef"
                    :options="serverLists"
                    :dropdown-match-select-width="false"
                    @change="selectChange"
                    @dropdown-visible-change="dropdownVisibleChange"
                    :field-names="{
                        label: 'name',
                        value: 'id',
                    }"
                >
                    <template #dropdownRender="{ menuNode: menu }">
                        <VNodes :vnodes="menu" />
                        <a-divider style="width: 500px; margin: 4px 0" />
                        <div
                            class="btn"
                            style="text-align: center"
                        >
                            <a-button
                                @click="addForeignClick"
                                size="small"
                            >
                                <PlusOutlined />新增业务类型
                            </a-button>
                        </div>
                    </template>
                </a-select>
                <a-input
                    size="small"
                    type="number"
                    class="moneyinput"
                    :disabled="inputDisable"
                    v-model:value="inputValue"
                >
                    <!-- <template #addonAfter>
                        <div
                            class="jsjicon"
                            @click="() => {}"
                        >
                            <SvgJisuanqi />
                        </div>
                    </template> -->
                </a-input>
                <div class="style1 d1">
                    <div>总金额</div>
                    <div>{{ inputValue || 0 }}</div>
                </div>
                <div class="style1 d2">
                    <div @click="setBusinessShow = true">设置业务类型</div>
                    <div @click="prefabricateClick">预置业务类型</div>
                </div>
            </div>
            <SetBusinessPop
                v-if="setBusinessShow"
                @close="setBusinessShow = false"
                @getlist="getlist"
            />
        </div>
    </Teleport>
</template>
<style lang="scss" scoped>
.cont {
    position: fixed;
    z-index: 1000;
    width: 200px;
    height: 200px;
    color: #666;
    background-color: #fff;
    border-radius: 5px;
    box-shadow: 0 0 5px #000;

    .txt {
        padding-left: 10px;
        font-size: 12px;
        font-weight: bold;
        line-height: 40px;
        cursor: move;
    }

    .moneyinput {
        margin-top: 10px;

        /* .ant-input-group-addon {
        } */
    }

    .jsjicon {
        cursor: pointer;
    }

    .box1 {
        width: 90%;
        margin-right: auto;
        margin-left: auto;
    }

    .close {
        position: absolute;
        top: 10px;
        right: 10px;
        cursor: pointer;
    }

    .style1 {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        font-size: 12px;
        line-height: 24px;
    }

    .d1 {
        margin-top: 15px;

        div:nth-child(2) {
            font-weight: bold;
            color: red;
        }
    }

    .d2 {
        margin-top: 10px;
        color: rgb(33 155 218);

        div {
            cursor: pointer;
        }
    }
}
</style>
