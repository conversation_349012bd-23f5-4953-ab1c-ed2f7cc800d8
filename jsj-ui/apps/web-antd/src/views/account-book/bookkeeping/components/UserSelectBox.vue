<script setup lang="ts">
import { computed, defineComponent, defineEmits, defineProps, ref, watch } from 'vue';

import { LoadingOutlined, PlusOutlined } from '@ant-design/icons-vue';

const { options, type, btntext } = defineProps<{
    btntext: string;
    options: any;
    type: string;
}>();
const emits = defineEmits(['selectChange', 'newlyAddedClick']);
const seachVal = ref<string>('');
const inputRef = ref<any>('');
const optonslist = ref<any[]>([]);
const itmclick = (itm: any) => {
    emits('selectChange', itm, type);
};
const inputClick = (e: any) => {
    e.preventDefault();
    e.stopPropagation();
};
// 新增摘要/科目
const newlyAddedClick = (e) => {
    e.preventDefault();
    e.stopPropagation();
    emits('newlyAddedClick');
};
watch(seachVal, (newVal, oldVal) => {
    console.log(newVal, oldVal);
    const arr = options.value.filter((v: any) => {
        return v.text.includes(newVal) || v.id.includes(newVal);
    });
    optonslist.value = arr;
});
</script>
<template>
    <div class="box">
        <a-input
            style="width: 100%; height: 30px"
            v-model:value="seachVal"
            ref="inputRef"
            placeholder=""
            class="mb-2 text-left"
            @click="inputClick"
        />
        <div
            v-if="options.value.length > 0"
            class="listcont"
        >
            <template v-if="seachVal === ''">
                <div
                    v-for="itm in options.value"
                    :key="itm.text"
                    class="itm"
                    @click="itmclick(itm)"
                >
                    {{ itm.text }}
                </div>
            </template>
            <template v-else>
                <div
                    v-for="itm in optonslist"
                    :key="itm.text"
                    class="itm"
                    @click="itmclick(itm)"
                >
                    {{ itm.text }}
                </div>
            </template>
        </div>
        <div
            v-if="options.value.length === 0"
            class="btn loading text-center"
        >
            加载中
            <LoadingOutlined />
        </div>
        <div
            v-if="options.value.length > 0"
            class="btn text-center"
            @click="newlyAddedClick"
        >
            <PlusOutlined />
            {{ btntext }}
        </div>
    </div>
</template>
<style lang="scss" scoped>
.box {
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
    padding: 4px;
    margin: 0;
    font-size: 14px;
    color: rgb(50 54 57 / 88%);
    list-style: none;
    background-color: hsl(0deg 0% 100%);
    border-radius: 8px;
    outline: none;
    box-shadow:
        0 6px 16px 0 rgb(0 0 0 / 8%),
        0 3px 6px -4px rgb(0 0 0 / 12%),
        0 9px 28px 8px rgb(0 0 0 / 5%);

    .listcont {
        flex: 1;
        overflow: auto;
    }

    .loading {
        margin-top: 20px;
        margin-bottom: 20px;
    }

    .itm,
    .btn {
        position: relative;
        box-sizing: border-box;
        display: block;
        min-height: 32px;
        padding: 5px 12px;
        font-size: 14px;
        color: rgb(50 54 57 / 88%);
        cursor: pointer;
        border-radius: 4px;
        transition: background 0.3s ease;
    }

    .btn {
        padding: 5px;
        background-color: rgb(50 54 57 / 4%);
    }

    .itm:hover {
        background-color: rgb(50 54 57 / 4%);
    }
}
</style>
