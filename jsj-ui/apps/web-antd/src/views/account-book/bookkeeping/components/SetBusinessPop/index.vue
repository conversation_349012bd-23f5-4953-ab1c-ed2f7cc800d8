<script setup lang="ts">
import { defineProps, h, onMounted, onUnmounted, reactive, ref, watch } from 'vue';

import { ExclamationCircleOutlined } from '@ant-design/icons-vue';
import { message, Modal } from 'ant-design-vue';

import { addServiceType, getDeleteServiceType, getServiceTypeList } from '#/api/account-book/bookkeeping/index';
import { useAbstractData, useSubjectData } from '#/hooks/account-book/voucher/index';
import { useGlobalLoading } from '#/hooks/useGlobalLoading';
import { uTgetChinesePinyinAbbreviation } from '#/utils/index';

const emits = defineEmits(['close', 'getlist']);
const useLoading = useGlobalLoading();
const [modal, contextHolder] = Modal.useModal();
const visible = true;
const useSubject = useSubjectData();
const formRef = ref<any>();
const lists = ref<any[]>();
const columns = [
    {
        title: '类型名称',
        dataIndex: 'typeName',
        key: 'typeName',
    },
    {
        title: '摘要',
        dataIndex: 'summary',
        key: 'summary',
    },
    {
        title: '借方科目',
        dataIndex: 'debitSubject',
        key: 'debitSubject',
    },
    {
        title: '贷方科目',
        dataIndex: 'creditSubject',
        key: 'creditSubject',
    },
    {
        title: '税金科目',
        dataIndex: 'taxSubject',
        key: 'taxSubject',
    },
    {
        title: '税金方向',
        dataIndex: 'taxDirection',
        key: 'taxDirection',
    },
    {
        title: '操作',
        dataIndex: 'operation',
        key: 'index',
    },
];
const stateform = reactive<any>({
    creditSubjectId: undefined, // 贷方科目
    debitSubjectId: undefined, // 借方科目
    mnemonicCode: '', // 助记码
    summary: '', // 新增摘要
    taxDirection: 'TAX_IN_DEBIT',
    taxSubjectId: undefined, // 税金科目
    typeName: '', // 类型名称
    debitOrCreditTaxes: undefined, // 税金方向
    autoLevel: false, // 凭证自动找平
});
const save = () => {
    formRef.value.validate().then((data: any) => {
        const debitdata = useSubject.selectdata.value.find((v: any) => v.id === stateform.debitSubjectId);
        const creditdata = useSubject.selectdata.value.find((v: any) => v.id === stateform.creditSubjectId);
        const taxdata = useSubject.selectdata.value.find((v: any) => v.id === stateform.taxSubjectId);
        const todata: any = {
            typeName: stateform.typeName,
            typeHelpCode: stateform.mnemonicCode,
            debitSubject: stateform.debitSubjectId,
            debitSubjectText: debitdata.text,
            summary: stateform.summary,
            creditSubject: stateform.creditSubjectId,
            creditSubjectText: creditdata.text,
        };
        if (stateform.taxSubjectId) {
            todata.taxSubject = stateform.taxSubjectId;
            todata.taxSubjectText = taxdata.text;
            todata.autoLevel = stateform.autoLevel;
            todata.debitOrCreditTaxes = stateform.debitOrCreditTaxes;
        }
        addServiceType(todata).then((res: any) => {
            console.log('222', res);
        });
    });
};
const inputblur = () => {
    stateform.mnemonicCode = uTgetChinesePinyinAbbreviation(stateform.typeName);
};
const getlist = () => {
    getServiceTypeList().then((res: any) => {
        if (res.returnCode === '200') {
            console.log('列表', res.data);
            lists.value = res.data;
        } else {
            message.warning(res.returnMsg);
        }
    });
};
onMounted(() => {
    getlist();
});
// 删除
const deleteData = (record: any, index: number) => {
    modal.confirm({
        title: '确认删除?',
        icon: h(ExclamationCircleOutlined),
        content: `请问您确定要删除类别 ${record.typeName} 么？`,
        async onOk() {
            useLoading.setShow(true);
            getDeleteServiceType(record.id)
                .then((res: any) => {
                    if (res.returnCode === '200') {
                        // 删除
                        // getlist();
                        lists.value?.splice(index, 1);
                        emits('getlist'); // 父弹窗从新拉去列表
                    } else {
                        message.warning(res.returnMsg);
                    }
                    useLoading.setShow(false);
                })
                .catch(() => {
                    useLoading.setShow(false);
                });
        },
        onCancel() {},
    });
};
</script>
<template>
    <a-modal
        v-model:open="visible"
        width="1000px"
        title="设置业务类型"
        :footer="false"
        :mask-closable="false"
        @cancel="emits('close')"
    >
        <div>
            <div class="formbox">
                <a-form
                    :model="stateform"
                    ref="formRef"
                    :label-col="{ span: 5 }"
                    :wrapper-col="{ span: 19 }"
                >
                    <a-row>
                        <a-col :span="12">
                            <a-form-item
                                name="typeName"
                                label="类型名称"
                                :rules="[{ required: true, message: '请填写类型名称' }]"
                            >
                                <a-input
                                    v-model:value="stateform.typeName"
                                    size="small"
                                    @blur="inputblur"
                                />
                            </a-form-item>
                        </a-col>
                        <a-col :span="12">
                            <a-form-item label="助记码">
                                <a-input
                                    v-model:value="stateform.mnemonicCode"
                                    size="small"
                                />
                            </a-form-item>
                        </a-col>
                        <a-col :span="12">
                            <a-form-item
                                name="summary"
                                label="摘要"
                                :rules="[{ required: true, message: '请填写摘要' }]"
                            >
                                <a-input
                                    v-model:value="stateform.summary"
                                    size="small"
                                />
                            </a-form-item>
                        </a-col>
                        <a-col :span="12">
                            <a-form-item
                                label="借方科目"
                                name="debitSubjectId"
                                :rules="[{ required: true, message: '请选择借方科目' }]"
                            >
                                <a-select
                                    v-model:value="stateform.debitSubjectId"
                                    placeholder="请选择借方科目"
                                    size="small"
                                    show-search
                                    :field-names="{
                                        label: 'text',
                                        value: 'id',
                                    }"
                                    :options="useSubject.selectdata.value"
                                />
                            </a-form-item>
                        </a-col>
                        <a-col :span="12">
                            <a-form-item
                                label="贷方科目"
                                name="creditSubjectId"
                                :rules="[{ required: true, message: '请选择贷方科目' }]"
                            >
                                <a-select
                                    v-model:value="stateform.creditSubjectId"
                                    placeholder="请选择贷方科目"
                                    size="small"
                                    show-search
                                    :field-names="{
                                        label: 'text',
                                        value: 'id',
                                    }"
                                    :options="useSubject.selectdata.value"
                                />
                            </a-form-item>
                        </a-col>
                        <a-col :span="12">
                            <a-form-item label="税金科目">
                                <a-select
                                    v-model:value="stateform.taxSubjectId"
                                    placeholder="请选择"
                                    show-search
                                    size="small"
                                    :field-names="{
                                        label: 'text',
                                        value: 'id',
                                    }"
                                    :options="useSubject.selectdata.value"
                                />
                            </a-form-item>
                        </a-col>
                        <a-col
                            :span="12"
                            v-if="stateform.taxSubjectId"
                        >
                            <a-form-item label="税金方向">
                                <a-radio-group
                                    name="debitOrCreditTaxes"
                                    v-model:value="stateform.debitOrCreditTaxes"
                                >
                                    <a-radio value="0">税金在借方</a-radio>
                                    <a-radio value="1">税金在贷方</a-radio>
                                </a-radio-group>
                            </a-form-item>
                        </a-col>
                        <a-col
                            :span="12"
                            v-if="stateform.taxSubjectId"
                        >
                            <a-form-item label="找平">
                                <a-checkbox v-model:checked="stateform.autoLevel">凭证自动找平</a-checkbox>
                            </a-form-item>
                        </a-col>
                    </a-row>
                </a-form>
                <a-button
                    type="primary"
                    size="small"
                    class="ml-3"
                    @click="save"
                >
                    保存
                </a-button>
            </div>
            <div class="tablebox mt-2">
                <a-table
                    :columns="columns"
                    :data-source="lists"
                    size="small"
                >
                    <template #bodyCell="{ column, text, record, index }">
                        <template v-if="column.dataIndex === 'operation'">
                            <a-button
                                danger
                                size="small"
                                @click="deleteData(record, index)"
                            >
                                删除
                            </a-button>
                        </template>
                    </template>
                </a-table>
            </div>
            <contextHolder />
        </div>
    </a-modal>
</template>
<style lang="scss" scoped></style>
