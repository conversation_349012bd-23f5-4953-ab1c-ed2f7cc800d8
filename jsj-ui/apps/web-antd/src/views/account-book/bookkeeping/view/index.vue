<script setup lang="ts">
import type { Dayjs } from 'dayjs';

import { onMounted, onUnmounted, reactive, ref, watch } from 'vue';

import {
    SvgAttention,
    SvgShortcutKeys,
    SvgUserADD,
    SvgUser<PERSON><PERSON><PERSON><PERSON>,
    Svg<PERSON>ser<PERSON><PERSON><PERSON>,
    Svg<PERSON>ser<PERSON><PERSON><PERSON>,
    Svg<PERSON>ser<PERSON><PERSON>,
    SvgUser<PERSON>,
    SvgUser<PERSON>hangyi,
    SvgUserXiayi,
    SvgUserXiugai,
    SvgWechatIcon,
} from '@vben/icons';

import Icon, { DownOutlined, UserOutlined } from '@ant-design/icons-vue';
import { message } from 'ant-design-vue';
import dayjs from 'dayjs';

import { changeVoucherNumber, getVoucherList } from '#/api/account-book/bookkeeping/index';
import { commondata } from '#/common/index';
import { useAbstractData, useSubjectData } from '#/hooks/account-book/voucher/index.js';
import { useGlobalLoading } from '#/hooks/useGlobalLoading';
import { uTgetTime } from '#/utils/index';
import OuterBoundary from '#/views/components/outer-boundary.vue';

const dateFormat = 'YYYY-MM';
type RangeValue = [Dayjs, Dayjs];
const currdate = uTgetTime();
const timeInterval = ref<RangeValue>([dayjs(`${currdate[0]}-${currdate[1]}`, dateFormat), dayjs(`${currdate[0]}-${currdate[1]}`, dateFormat)]);
const seachInput = ref<string>('');
const moreSeachShow = ref<boolean>(false);
const sortType = ref<string>('0');
const voucherList = ref<any[]>([]);
const changeVoucherNo = ref<string>(''); // 修改编号输入框值
const useLoading = useGlobalLoading();
const currPage = ref<number>(0);
const selectAll = ref<boolean>(false);
const allTotalLength = ref<number>(0);
let currentModificationMumberIndex = -1;
type BookTypeItm = {
    value: string;
};
// 更多搜索
const searchesState = reactive({
    book_type_val: '不限',
    voucherNumber: '', // 更多搜索里的凭证号码
    checked1: false,
    checked2: false,
    starTime: dayjs(`${currdate[0]}-${currdate[1]}`, dateFormat),
    endTime: dayjs(`${currdate[0]}-${currdate[1]}`, dateFormat),
    accountNumber: '', // 科目编号
    accountingType: 'All', // 辅助核算
    accountingInput: '', // 辅助核算输入框
    moneyMin: '', // 最小
    moneyMax: '', // 最大
    abstract: '', // 摘要
    source: 'All', // 来源
});
const useSubject = useSubjectData();
// 搜索凭证
const onSearchInput = () => {};
// 更多查询
const confirmSearch = () => {};
const cancelSearch = () => {};
const getList = async (page: number, isMore?: boolean) => {
    const todata: any = {
        voucherWord: searchesState.book_type_val,
        auxiliaryType: searchesState.accountingType, // 辅助核算
        voucherType: searchesState.source,
        startDate: timeInterval.value[0].format('YYYY-MM'),
        endDate: timeInterval.value[1].format('YYYY-MM'),
    };
    if (searchesState.voucherNumber || seachInput.value) {
        todata.voucherNo = searchesState.voucherNumber || seachInput.value;
    }
    if (searchesState.accountNumber) {
        todata.subjectCode = searchesState.accountNumber;
    }
    if (searchesState.accountingInput || seachInput.value) {
        todata.auxiliaryCode = searchesState.accountingInput || seachInput.value;
    }
    if (searchesState.moneyMin) {
        todata.startPrice = searchesState.moneyMin;
    }
    if (searchesState.moneyMax) {
        todata.endPrice = searchesState.moneyMax;
    }
    if (searchesState.abstract) {
        todata.summary = searchesState.abstract;
    }
    if (searchesState.checked1) {
        todata.isCashflow = true;
    }
    if (searchesState.checked2) {
        todata.isAuditComment = true;
    }
    useLoading.setShow(true, { text: '加载中...' });
    /**
     * {
            "voucherWord": "记",
            "auxiliaryType": "Supplier",
            "voucherType": "NORMAL",
            "startDate": "2025-05",
            "endDate": "2025-05",
            "voucherNo": "1212",
            "subjectCode": "1001",
            "auxiliaryCode": "天津",
            "startPrice": "1",
            "endPrice": "180",
            "summary": "这是摘要",
            "isCashflow": true,
            "isAuditComment": true
        }
     */
    getVoucherList(todata, page, sortType.value)
        .then((res: any) => {
            if (res.returnCode === '200') {
                currPage.value = page;
                // 数据需要处理下添加两个参数
                // 是否选中参数 修改凭证字号的输入框是否显示
                res.data.map((v: any) => {
                    v.isChecked = false; // 是否选中
                    v.inputIsShow = false;
                });
                // 总条数
                allTotalLength.value = res.data.total;
                voucherList.value = res.data;
            } else {
                message.warning(res.returnMsg);
            }
            useLoading.setShow(false);
        })
        .catch(() => {
            useLoading.setShow(false);
        });
};
onMounted(() => {
    if (useSubject.selectdata.value.length === 0) {
        useSubject.fetchData();
    }
    getList(0);
});
const documentclick = () => {
    moreSeachShow.value = false;
    if (currentModificationMumberIndex !== -1 && voucherList.value[currentModificationMumberIndex]) {
        voucherList.value[currentModificationMumberIndex].inputIsShow = false;
        currentModificationMumberIndex = -1;
    }
};
onMounted(() => {
    document.addEventListener('click', documentclick);
});
onUnmounted(() => {
    document.removeEventListener('click', documentclick);
});
const moreSeachShowFu = () => {
    moreSeachShow.value = !moreSeachShow.value;
};
// 新增凭证
const addVoucher = () => {};
const timeIntervalChange = (e: any, data: any) => {
    searchesState.starTime = dayjs(`${data[0]}`, dateFormat);
    searchesState.endTime = dayjs(`${data[1]}`, dateFormat);
    getList(0);
};
// 全选
const selectAllChange = () => {
    if (selectAll.value) {
        // 全选操作
        voucherList.value.map((v) => {
            v.isChecked = true;
        });
    } else {
        voucherList.value.map((v) => {
            v.isChecked = false;
        });
    }
    console.log(898, selectAll.value);
};
// 单选事件
const singleChoice = () => {
    let isallselect = true; // 是否全选
    voucherList.value.map((v) => {
        if (!v.isChecked) {
            isallselect = false;
        }
    });
    if (isallselect) {
        selectAll.value = true;
    } else {
        selectAll.value = false;
    }
};
// 修改凭证编号输入框的显示
const modifyVoucherNumber = (index: number) => {
    if (currentModificationMumberIndex === index) {
        voucherList.value[index].inputIsShow = !voucherList.value[index].inputIsShow;
        return;
    }
    changeVoucherNo.value = '';
    voucherList.value[index].inputIsShow = true;
    if (currentModificationMumberIndex !== -1 && voucherList.value[currentModificationMumberIndex]) {
        voucherList.value[currentModificationMumberIndex].inputIsShow = false;
    }
    currentModificationMumberIndex = index;
};
const saveVoucherNoClick = (index: number) => {
    // 凭证编号保存事件
    if (!changeVoucherNo.value) {
        message.warning('请输入编号');
        return;
    }
    const id = voucherList.value[index].id;
    changeVoucherNumber(id, changeVoucherNo.value).then((res: any) => {
        console.log(7878, res);
    });
};
// 更多查询点击事件
const moreInquiries = () => {
    getList(0);
};
// 更多查询重置
const resetmore = () => {
    searchesState.book_type_val = '不限';
    searchesState.voucherNumber = '';
    searchesState.checked1 = false;
    searchesState.checked2 = false;
    searchesState.accountNumber = '';
    searchesState.accountingType = 'All';
    searchesState.accountingInput = '';
    searchesState.moneyMin = '';
    searchesState.moneyMax = '';
    searchesState.abstract = '';
    searchesState.source = 'All';
};
// 页码上的点击事件
const pageClick = (page: any) => {
    console.log(page);
    getList(page - 1);
};
watch([timeInterval, sortType], ([new1, newSort]) => {
    console.log('shun', new1, newSort);
});
watch(sortType, (newSort) => {
    console.log('shun11', newSort);
    getList(0);
});
</script>
<template>
    <OuterBoundary>
        <div class="cont flex flex-1 flex-col rounded-md bg-white">
            <div class="flex flex-1 flex-col">
                <div class="p-2">
                    <a-flex justify="space-between">
                        <a-space>
                            <a-range-picker
                                v-model:value="timeInterval"
                                picker="month"
                                size="small"
                                @change="timeIntervalChange"
                            />
                            <a-input-search
                                v-model:value="seachInput"
                                placeholder="凭证号"
                                style="width: 100px"
                                @search="onSearchInput"
                                size="small"
                            />
                            <div class="move-seach">
                                <a-button
                                    type="primary"
                                    size="small"
                                    @click.stop="moreSeachShowFu"
                                >
                                    更多查询
                                </a-button>
                                <div
                                    v-if="moreSeachShow"
                                    class="hideinfo"
                                    @click.stop
                                >
                                    <ul class="">
                                        <li class="li1">会计期间</li>
                                        <li class="ii2">
                                            <a-date-picker
                                                size="small"
                                                picker="month"
                                                class="stylew1"
                                                v-model:value="searchesState.starTime"
                                            />
                                            至
                                            <a-date-picker
                                                size="small"
                                                picker="month"
                                                class="stylew1"
                                                v-model:value="searchesState.endTime"
                                            />
                                        </li>
                                    </ul>
                                    <ul>
                                        <li class="li1">凭证字</li>
                                        <li class="ii2">
                                            <a-select
                                                size="small"
                                                :options="[{ value: '不限' }].concat(commondata.bookTypes)"
                                                v-model:value="searchesState.book_type_val"
                                                :field-names="{ label: 'value', value: 'value' }"
                                                class="stylew2"
                                            />
                                            至
                                            <a-input
                                                v-model:value="searchesState.voucherNumber"
                                                placeholder="凭证号"
                                                class="stylew3"
                                                size="small"
                                            />
                                        </li>
                                    </ul>
                                    <ul>
                                        <li class="li1">科目编号</li>
                                        <li class="ii2">
                                            <a-select
                                                :field-names="{
                                                    label: 'text',
                                                    value: 'code',
                                                }"
                                                show-search
                                                :options="useSubject.selectdata"
                                                class="stylew4"
                                                size="small"
                                                v-model:value="searchesState.accountNumber"
                                            />
                                        </li>
                                    </ul>
                                    <ul>
                                        <li class="li1">辅助核算</li>
                                        <li class="ii2">
                                            <a-select
                                                size="small"
                                                style="width: 100px"
                                                :options="commondata.auxiliaryAccounting"
                                                class="stylew2"
                                                v-model:value="searchesState.accountingType"
                                            />
                                            至
                                            <a-input
                                                v-model:value="searchesState.accountingInput"
                                                placeholder="请输入编码名称"
                                                class="stylew3"
                                                size="small"
                                            />
                                        </li>
                                    </ul>
                                    <ul>
                                        <li class="li1">金额</li>
                                        <li class="ii2">
                                            <a-input
                                                v-model:value="searchesState.moneyMin"
                                                placeholder="最低金额"
                                                class="stylew1"
                                                size="small"
                                            />
                                            至
                                            <a-input
                                                v-model:value="searchesState.moneyMax"
                                                placeholder="最高金额"
                                                class="stylew1"
                                                size="small"
                                            />
                                        </li>
                                    </ul>
                                    <ul>
                                        <li class="li1">摘要</li>
                                        <li class="ii2">
                                            <a-input
                                                v-model:value="searchesState.abstract"
                                                placeholder="请输入凭证摘要"
                                                class="stylew4"
                                                size="small"
                                            />
                                        </li>
                                    </ul>
                                    <ul>
                                        <li class="li1">来源</li>
                                        <li class="ii2">
                                            <a-select
                                                size="small"
                                                style="width: 100px"
                                                :options="commondata.voucherType"
                                                class="stylew4"
                                                v-model:value="searchesState.source"
                                            />
                                        </li>
                                    </ul>
                                    <ul>
                                        <li class="ii2">
                                            <a-checkbox v-model:checked="searchesState.checked1"> 只显示设置了有现金流量项目科目的凭证</a-checkbox>
                                        </li>
                                    </ul>
                                    <ul>
                                        <li class="ii2">
                                            <a-checkbox v-model:checked="searchesState.checked2"> 只显示有标注的凭证</a-checkbox>
                                        </li>
                                    </ul>
                                    <a-divider style="margin: 10px 0" />
                                    <ul>
                                        <li class="li2"></li>
                                        <li class="li3">
                                            <a-button
                                                size="small"
                                                @click="resetmore"
                                            >
                                                重置
                                            </a-button>
                                            &nbsp;
                                            <a-button
                                                size="small"
                                                type="primary"
                                                @click="moreInquiries"
                                            >
                                                确定
                                            </a-button>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                            <a-button
                                type="primary"
                                size="small"
                                @click="addVoucher"
                            >
                                新增凭证
                            </a-button>
                            <a-dropdown>
                                <a-button
                                    type="primary"
                                    size="small"
                                >
                                    凭证导入
                                    <DownOutlined />
                                </a-button>
                                <template #overlay>
                                    <a-menu>
                                        <a-menu-item> 导入 </a-menu-item>
                                        <a-menu-item> 下载模板 </a-menu-item>
                                    </a-menu>
                                </template>
                            </a-dropdown>
                        </a-space>
                        <a-space>
                            <a-button size="small">复制</a-button>
                            <a-button size="small">删除</a-button>
                            <a-button-group>
                                <a-button size="small">审核</a-button>
                                <a-button size="small">反审核</a-button>
                            </a-button-group>
                            <a-button-group>
                                <a-dropdown>
                                    <a-button size="small">
                                        选择排序方式
                                        <DownOutlined />
                                    </a-button>
                                    <template #overlay>
                                        <a-menu>
                                            <a-radio-group v-model:value="sortType">
                                                <a-menu-item>
                                                    <a-radio value="0"> 按凭证号排序 </a-radio>
                                                </a-menu-item>
                                                <a-menu-item>
                                                    <a-radio value="1">按凭证日期排序</a-radio>
                                                </a-menu-item>
                                                <a-menu-item>
                                                    <a-radio value="2">按录入时间排序</a-radio>
                                                </a-menu-item>
                                            </a-radio-group>
                                        </a-menu>
                                    </template>
                                </a-dropdown>
                                <a-button size="small">断号重排</a-button>
                            </a-button-group>
                            <a-button size="small">打印</a-button>
                        </a-space>
                    </a-flex>
                </div>
                <div
                    class="listcont m-2 flex-1"
                    flex="1"
                >
                    <div class="tablelist">
                        <table>
                            <thead class="sticky-row">
                                <tr>
                                    <th>
                                        <a-checkbox
                                            v-model:checked="selectAll"
                                            @change="selectAllChange"
                                        />
                                    </th>
                                    <th>摘要</th>
                                    <th>科目</th>
                                    <th>借方金额</th>
                                    <th>贷方金额</th>
                                </tr>
                            </thead>
                            <tbody>
                                <template
                                    v-for="(item, i) in voucherList"
                                    :key="item.id"
                                >
                                    <tr>
                                        <td :rowspan="item.detail.length + 2">
                                            <div class="h-[100%] pt-2 text-center">
                                                <a-checkbox
                                                    @change="singleChoice"
                                                    v-model:checked="item.isChecked"
                                                />
                                            </div>
                                        </td>
                                        <td colspan="4">
                                            <div class="btns d1">
                                                <div>
                                                    凭证字号：<span class="s1">
                                                        <span
                                                            @click.stop="
                                                                () => {
                                                                    modifyVoucherNumber(i);
                                                                }
                                                            "
                                                            >{{ item.code }}
                                                        </span>
                                                        <div
                                                            class="inputhide"
                                                            v-if="item.inputIsShow"
                                                            @click.stop
                                                        >
                                                            <a-input
                                                                style="width: 80px"
                                                                size="small"
                                                                v-model:value="changeVoucherNo"
                                                            />
                                                            &nbsp;
                                                            <a-button
                                                                size="small"
                                                                type="primary"
                                                                @click="
                                                                    () => {
                                                                        saveVoucherNoClick(i);
                                                                    }
                                                                "
                                                            >
                                                                保存
                                                            </a-button>
                                                        </div>
                                                    </span>
                                                    日期：{{ item.date }} 凭证类型：{{ item.type }}
                                                </div>
                                                <div>
                                                    <div
                                                        v-if="i > 0"
                                                        class="btn"
                                                    >
                                                        <Icon class="icon">
                                                            <SvgUserShangyi />
                                                        </Icon>
                                                        <span>上移</span>
                                                    </div>
                                                    <div
                                                        v-if="i !== voucherList.length - 1"
                                                        class="btn"
                                                    >
                                                        <Icon class="icon">
                                                            <SvgUserXiayi />
                                                        </Icon>
                                                        <span>下移</span>
                                                    </div>
                                                    <div class="btn">
                                                        <Icon class="icon">
                                                            <SvgUserLook />
                                                        </Icon>
                                                        <span>查看</span>
                                                    </div>
                                                    <div class="btn">
                                                        <Icon class="icon">
                                                            <SvgUserXiugai />
                                                        </Icon>
                                                        <span>修改</span>
                                                    </div>
                                                    <div class="btn">
                                                        <Icon class="icon">
                                                            <SvgUserADD />
                                                        </Icon>
                                                        <span>插入</span>
                                                    </div>
                                                    <div class="btn">
                                                        <Icon class="icon">
                                                            <SvgUserDayin />
                                                        </Icon>
                                                        <span>打印</span>
                                                    </div>
                                                    <div class="btn">
                                                        <Icon class="icon">
                                                            <SvgUserChongxiao />
                                                        </Icon>
                                                        <span>冲销</span>
                                                    </div>
                                                    <div class="btn">
                                                        <Icon class="icon">
                                                            <SvgUserShanchu />
                                                        </Icon>
                                                        <span>删除</span>
                                                    </div>
                                                    <div class="btn">
                                                        <Icon class="icon">
                                                            <SvgUserBiaozhu />
                                                        </Icon>
                                                        <span>标注</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </td>
                                    </tr>
                                    <tr
                                        v-for="itm in item.detail"
                                        :key="itm.id"
                                    >
                                        <td>
                                            <div class="d1">{{ itm.summary }}</div>
                                        </td>
                                        <td>
                                            <div class="d1">{{ itm.subjectName }}</div>
                                        </td>
                                        <td>{{ itm.debit }}</td>
                                        <td>
                                            <div class="d1">{{ itm.credit }}</div>
                                        </td>
                                    </tr>
                                    <tr class="tbfooter">
                                        <td colspan="2">
                                            <div class="d1">合计：{{ item.totalAmount }}</div>
                                        </td>
                                        <td>
                                            <div class="d1">{{ item.debit }}</div>
                                        </td>
                                        <td>
                                            <div class="d1">{{ item.credit }}</div>
                                        </td>
                                    </tr>
                                </template>
                            </tbody>
                        </table>
                        <div
                            v-if="voucherList.length === 0"
                            class="empyt mt-10 text-center"
                        >
                            暂无数据
                        </div>
                    </div>
                </div>
            </div>
            <a-divider style="margin: 4px 0" />
            <div class="bottom-box flex justify-between p-3">
                <div>共计{{ allTotalLength || 0 }}条凭证</div>
                <div>
                    <a-pagination
                        v-model:current="currPage"
                        :total="allTotalLength"
                        default-page-size="20"
                        show-less-items
                        @change="pageClick"
                    />
                </div>
            </div>
        </div>
    </OuterBoundary>
</template>
<style lang="scss" scoped>
.sticky-row {
    position: sticky;
    top: 0; //固定行
    z-index: 1;
    background-color: rgb(250 250 250);

    tr {
        border: solid 1px rgb(233 233 233);
    }

    th {
        outline: rgb(233 233 233) solid 0.5px;
    }
}

.move-seach {
    position: relative;
    display: inline-block;

    .hideinfo {
        position: absolute;
        top: 30px;
        left: 0;
        z-index: 2;
        width: 465px;
        padding: 10px;
        background-color: #fff;
        border: solid 1px #eee;
        border-radius: 10px;

        .stylew1 {
            width: 170px;
        }

        .stylew2 {
            width: 100px;
        }

        .stylew3 {
            width: 240px;
        }

        .stylew4 {
            width: 362px;
        }

        ul {
            display: flex;
            margin: 5px 0;

            .li1 {
                width: 80px;
            }

            .li2 {
                flex: 1;
            }
        }
    }
}

.listcont {
    position: relative;

    .tablelist {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        overflow: auto;
    }

    table {
        width: 100%;
        font-size: 12px;

        thead {
            font-size: 14px;
            background-color: rgb(250 250 250);
        }

        .tbfooter {
            font-size: 14px;
            font-weight: 500;
            background-color: rgb(254 251 242);
        }

        th,
        td {
            height: 40px;
            border: solid 1px rgb(233 233 233);
        }

        .d1 {
            margin-right: 10px;
            margin-left: 10px;
        }

        .btns {
            display: flex;
            flex-direction: row;
            justify-content: space-between;

            .s1 {
                position: relative;
                color: rgb(22 119 255);
                cursor: pointer;

                .inputhide {
                    position: absolute;
                    top: 30px;
                    left: 0;
                    width: 160px;
                    padding: 5px;
                    background-color: #fff;
                    border: solid 1px #eee;
                    border-radius: 5px;
                }
            }

            .btn {
                display: inline-block;
                margin: 0 5px;
                font-size: 0;
                color: rgb(22 119 255);
                cursor: pointer;

                span {
                    font-size: 12px;
                }

                .icon {
                    display: inline-block;
                    margin-top: -1px;
                    font-size: 20px;
                    vertical-align: top;
                }
            }
        }
    }
}
</style>
