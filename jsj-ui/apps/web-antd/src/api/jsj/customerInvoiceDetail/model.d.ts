import type { BaseEntity, PageQuery } from '#/api/common';

export interface CustomerInvoiceDetailVO {
  /**
   * 发票明细ID
   */
  invoiceDetailId: number | string;

  /**
   * 发票ID
   */
  invoiceId: number | string;

  /**
   * 货物或应税劳务名称
   */
  costName: string;

  /**
   * 规格型号
   */
  specificationModel: string;

  /**
   * 单位
   */
  unit: string;

  /**
   * 数量
   */
  count: number;

  /**
   * 单价
   */
  unitPrice: number;

  /**
   * 金额
   */
  amount: number;

  /**
   * 税率
   */
  taxRate: number;

  /**
   * 税额
   */
  taxAmount: number;

  /**
   * 价税合计
   */
  totalPrice: number;

  /**
   * 状态（0正常 1停用）
   */
  status: string;

  /**
   * 备注
   */
  remark: string;
}

export interface CustomerInvoiceDetailForm extends BaseEntity {
  /**
   * 发票明细ID
   */
  invoiceDetailId?: number | string;

  /**
   * 发票ID
   */
  invoiceId?: number | string;

  /**
   * 货物或应税劳务名称
   */
  costName?: string;

  /**
   * 规格型号
   */
  specificationModel?: string;

  /**
   * 单位
   */
  unit?: string;

  /**
   * 数量
   */
  count?: number;

  /**
   * 单价
   */
  unitPrice?: number;

  /**
   * 金额
   */
  amount?: number;

  /**
   * 税率
   */
  taxRate?: number;

  /**
   * 税额
   */
  taxAmount?: number;

  /**
   * 价税合计
   */
  totalPrice?: number;

  /**
   * 状态（0正常 1停用）
   */
  status?: string;

  /**
   * 备注
   */
  remark?: string;
}

export interface CustomerInvoiceDetailQuery extends PageQuery {
  /**
   * 发票ID
   */
  invoiceId?: number | string;

  /**
   * 货物或应税劳务名称
   */
  costName?: string;

  /**
   * 规格型号
   */
  specificationModel?: string;

  /**
   * 单位
   */
  unit?: string;

  /**
   * 数量
   */
  count?: number;

  /**
   * 单价
   */
  unitPrice?: number;

  /**
   * 金额
   */
  amount?: number;

  /**
   * 税率
   */
  taxRate?: number;

  /**
   * 税额
   */
  taxAmount?: number;

  /**
   * 价税合计
   */
  totalPrice?: number;

  /**
   * 状态（0正常 1停用）
   */
  status?: string;

  /**
   * 日期范围参数
   */
  params?: any;
}
