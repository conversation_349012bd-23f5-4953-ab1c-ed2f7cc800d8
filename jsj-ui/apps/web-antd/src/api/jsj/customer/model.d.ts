import type { BaseEntity, PageQuery } from '#/api/common';

export interface CustomerVO {
  /**
   * 客户ID
   */
  customerId: number | string;

  /**
   * 客户名称
   */
  customerName: string;

  /**
   * 计税方式（SIMPLE简易征收/NORMAL一般计税/ALL任意）
   */
  computeType: string;

  /**
   * 纳税人类型（SMALL小规模纳税人/GENERAL一般纳税人）
   */
  taxType: string;

  /**
   * 主要收入（工业收入/货物收入/服务收入）
   */
  incomeType: string;

  /**
   * 备注
   */
  remark: string;
}

export interface CustomerForm extends BaseEntity {
  /**
   * 客户ID
   */
  customerId?: number | string;

  /**
   * 客户名称
   */
  customerName?: string;

  /**
   * 计税方式（SIMPLE简易征收/NORMAL一般计税/ALL任意）
   */
  computeType?: string;

  /**
   * 纳税人类型（SMALL小规模纳税人/GENERAL一般纳税人）
   */
  taxType?: string;

  /**
   * 主要收入（工业收入/货物收入/服务收入）
   */
  incomeType?: string;

  /**
   * 备注
   */
  remark?: string;
}

export interface CustomerQuery extends PageQuery {
  /**
   * 客户名称
   */
  customerName?: string;

  /**
   * 计税方式（SIMPLE简易征收/NORMAL一般计税/ALL任意）
   */
  computeType?: string;

  /**
   * 纳税人类型（SMALL小规模纳税人/GENERAL一般纳税人）
   */
  taxType?: string;

  /**
   * 主要收入（工业收入/货物收入/服务收入）
   */
  incomeType?: string;

  /**
   * 日期范围参数
   */
  params?: any;
}
