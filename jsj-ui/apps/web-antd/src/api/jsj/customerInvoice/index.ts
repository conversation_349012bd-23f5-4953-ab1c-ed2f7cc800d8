import type {
  CustomerInvoiceForm,
  CustomerInvoiceQuery,
  CustomerInvoiceVO,
} from './model';

import type { ID, IDS, PageResult } from '#/api/common';

import { commonExport } from '#/api/helper';
import { requestClient } from '#/api/request';

/**
 * 查询客户发票列表
 * @param params
 * @returns 客户发票列表
 */
export function customerInvoiceList(params?: CustomerInvoiceQuery) {
  return requestClient.get<PageResult<CustomerInvoiceVO>>(
    '/jsj/customerInvoice/list',
    { params },
  );
}

/**
 * 导出客户发票列表
 * @param params
 * @returns 客户发票列表
 */
export function customerInvoiceExport(params?: CustomerInvoiceQuery) {
  return commonExport('/jsj/customerInvoice/export', params ?? {});
}

/**
 * 查询客户发票详情
 * @param invoiceId id
 * @returns 客户发票详情
 */
export function customerInvoiceInfo(invoiceId: ID) {
  return requestClient.get<CustomerInvoiceVO>(
    `/jsj/customerInvoice/${invoiceId}`,
  );
}

/**
 * 新增客户发票
 * @param data
 * @returns void
 */
export function customerInvoiceAdd(data: CustomerInvoiceForm) {
  return requestClient.postWithMsg<void>('/jsj/customerInvoice', data);
}

/**
 * 更新客户发票
 * @param data
 * @returns void
 */
export function customerInvoiceUpdate(data: CustomerInvoiceForm) {
  return requestClient.putWithMsg<void>('/jsj/customerInvoice', data);
}

/**
 * 删除客户发票
 * @param invoiceId id
 * @returns void
 */
export function customerInvoiceRemove(invoiceId: ID | IDS) {
  return requestClient.deleteWithMsg<void>(`/jsj/customerInvoice/${invoiceId}`);
}
