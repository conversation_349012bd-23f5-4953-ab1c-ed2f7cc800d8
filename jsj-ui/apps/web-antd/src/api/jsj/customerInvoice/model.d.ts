import type { BaseEntity, PageQuery } from '#/api/common';

export interface CustomerInvoiceVO {
  /**
   * 发票ID
   */
  invoiceId: number | string;

  /**
   * 老jsj发票id
   */
  oldInvoiceId: number | string;

  /**
   * 客户ID
   */
  customerId: number | string;

  /**
   * 发票代码
   */
  invoiceCode: string;

  /**
   * 发票号码
   */
  invoiceNumber: string;

  /**
   * 数电票号码
   */
  digitalInvoiceNumber: string;

  /**
   * 销方识别号
   */
  sellerTaxId: number | string;

  /**
   * 销方名称
   */
  sellerName: string;

  /**
   * 购方识别号
   */
  buyerTaxId: number | string;

  /**
   * 购方名称
   */
  buyerName: string;

  /**
   * 开票日期
   */
  invoiceDate: string;

  /**
   * 金额
   */
  amount: string;

  /**
   * 税额
   */
  taxAmount: string;

  /**
   * 价税合计
   */
  totalAmount: string;

  /**
   * 发票来源
   */
  invoiceSource: string;

  /**
   * 发票票种
   */
  invoiceType: string;

  /**
   * 是否正数发票
   */
  isPositiveInvoice: number;

  /**
   * 发票风险等级
   */
  riskLevel: number;

  /**
   * 开票人
   */
  issuer: string;

  /**
   * 状态
   */
  invoiceStatus: number;

  /**
   * 发票来源
   */
  source: string;

  /**
   * 发票类型
   */
  type: string;

  /**
   * 发票地址url
   */
  invoiceOssUrl: string;

  /**
   * 备注
   */
  remark: string;
}

export interface CustomerInvoiceForm extends BaseEntity {
  /**
   * 发票ID
   */
  invoiceId?: number | string;

  /**
   * 老jsj发票id
   */
  oldInvoiceId?: number | string;

  /**
   * 客户ID
   */
  customerId?: number | string;

  /**
   * 发票代码
   */
  invoiceCode?: string;

  /**
   * 发票号码
   */
  invoiceNumber?: string;

  /**
   * 数电票号码
   */
  digitalInvoiceNumber?: string;

  /**
   * 销方识别号
   */
  sellerTaxId?: number | string;

  /**
   * 销方名称
   */
  sellerName?: string;

  /**
   * 购方识别号
   */
  buyerTaxId?: number | string;

  /**
   * 购方名称
   */
  buyerName?: string;

  /**
   * 开票日期
   */
  invoiceDate?: string;

  /**
   * 金额
   */
  amount?: string;

  /**
   * 税额
   */
  taxAmount?: string;

  /**
   * 价税合计
   */
  totalAmount?: string;

  /**
   * 发票来源
   */
  invoiceSource?: string;

  /**
   * 发票票种
   */
  invoiceType?: string;

  /**
   * 是否正数发票
   */
  isPositiveInvoice?: number;

  /**
   * 发票风险等级
   */
  riskLevel?: number;

  /**
   * 开票人
   */
  issuer?: string;

  /**
   * 状态
   */
  invoiceStatus?: number;

  /**
   * 发票来源
   */
  source?: string;

  /**
   * 发票类型
   */
  type?: string;

  /**
   * 发票地址url
   */
  invoiceOssUrl?: string;

  /**
   * 备注
   */
  remark?: string;
}

export interface CustomerInvoiceQuery extends PageQuery {
  /**
   * 老jsj发票id
   */
  oldInvoiceId?: number | string;

  /**
   * 客户ID
   */
  customerId?: number | string;

  /**
   * 发票代码
   */
  invoiceCode?: string;

  /**
   * 发票号码
   */
  invoiceNumber?: string;

  /**
   * 数电票号码
   */
  digitalInvoiceNumber?: string;

  /**
   * 销方识别号
   */
  sellerTaxId?: number | string;

  /**
   * 销方名称
   */
  sellerName?: string;

  /**
   * 购方识别号
   */
  buyerTaxId?: number | string;

  /**
   * 购方名称
   */
  buyerName?: string;

  /**
   * 开票日期
   */
  invoiceDate?: string;

  /**
   * 金额
   */
  amount?: string;

  /**
   * 税额
   */
  taxAmount?: string;

  /**
   * 价税合计
   */
  totalAmount?: string;

  /**
   * 发票来源
   */
  invoiceSource?: string;

  /**
   * 发票票种
   */
  invoiceType?: string;

  /**
   * 是否正数发票
   */
  isPositiveInvoice?: number;

  /**
   * 发票风险等级
   */
  riskLevel?: number;

  /**
   * 开票人
   */
  issuer?: string;

  /**
   * 状态
   */
  invoiceStatus?: number;

  /**
   * 发票来源
   */
  source?: string;

  /**
   * 发票类型
   */
  type?: string;

  /**
   * 发票地址url
   */
  invoiceOssUrl?: string;

  /**
   * 日期范围参数
   */
  params?: any;
}
