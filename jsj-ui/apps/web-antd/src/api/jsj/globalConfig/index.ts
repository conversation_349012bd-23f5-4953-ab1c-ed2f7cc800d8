import type {
  GlobalConfigForm,
  GlobalConfigQuery,
  GlobalConfigVO,
} from './model';

import type { ID, IDS, PageResult } from '#/api/common';

import { commonExport } from '#/api/helper';
import { requestClient } from '#/api/request';

/**
 * 查询业务配置项管理列表
 * @param params
 * @returns 业务配置项管理列表
 */
export function globalConfigList(params?: GlobalConfigQuery) {
  return requestClient.get<PageResult<GlobalConfigVO>>(
    '/jsj/globalConfig/list',
    { params },
  );
}

/**
 * 导出业务配置项管理列表
 * @param params
 * @returns 业务配置项管理列表
 */
export function globalConfigExport(params?: GlobalConfigQuery) {
  return commonExport('/jsj/globalConfig/export', params ?? {});
}

/**
 * 查询业务配置项管理详情
 * @param configId id
 * @returns 业务配置项管理详情
 */
export function globalConfigInfo(configId: ID) {
  return requestClient.get<GlobalConfigVO>(`/jsj/globalConfig/${configId}`);
}

/**
 * 新增业务配置项管理
 * @param data
 * @returns void
 */
export function globalConfigAdd(data: GlobalConfigForm) {
  return requestClient.postWithMsg<void>('/jsj/globalConfig', data);
}

/**
 * 更新业务配置项管理
 * @param data
 * @returns void
 */
export function globalConfigUpdate(data: GlobalConfigForm) {
  return requestClient.putWithMsg<void>('/jsj/globalConfig', data);
}

/**
 * 删除业务配置项管理
 * @param configId id
 * @returns void
 */
export function globalConfigRemove(configId: ID | IDS) {
  return requestClient.deleteWithMsg<void>(`/jsj/globalConfig/${configId}`);
}
