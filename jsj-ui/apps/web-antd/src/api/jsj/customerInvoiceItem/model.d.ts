import type { BaseEntity, PageQuery } from '#/api/common';

export interface CustomerInvoiceItemVO {
  /**
   * 发票明细ID
   */
  invoiceDetailId: number | string;

  /**
   * 客户ID
   */
  customerId: number | string;

  /**
   * 老jsj发票明细ID
   */
  oldInvoiceDetailId: number | string;

  /**
   * 发票ID
   */
  invoiceId: number | string;

  /**
   * 税收分类编码
   */
  taxClassificationCode: string;

  /**
   * 特定业务类型
   */
  specialBusinessType: string;

  /**
   * 货物或应税劳务名称
   */
  goodsOrServiceName: string;

  /**
   * 规格型号
   */
  specificationModel: string;

  /**
   * 单位
   */
  unit: string;

  /**
   * 数量
   */
  quantity: string;

  /**
   * 单价
   */
  unitPrice: string;

  /**
   * 金额
   */
  amount: string;

  /**
   * 税率
   */
  taxRate: string;

  /**
   * 税额
   */
  taxAmount: string;

  /**
   * 价税合计
   */
  totalAmount: string;

  /**
   * 资金场景
   */
  financialScenario: string;

  /**
   * 备注
   */
  remark: string;
}

export interface CustomerInvoiceItemForm extends BaseEntity {
  /**
   * 发票明细ID
   */
  invoiceDetailId?: number | string;

  /**
   * 客户ID
   */
  customerId?: number | string;

  /**
   * 老jsj发票明细ID
   */
  oldInvoiceDetailId?: number | string;

  /**
   * 发票ID
   */
  invoiceId?: number | string;

  /**
   * 税收分类编码
   */
  taxClassificationCode?: string;

  /**
   * 特定业务类型
   */
  specialBusinessType?: string;

  /**
   * 货物或应税劳务名称
   */
  goodsOrServiceName?: string;

  /**
   * 规格型号
   */
  specificationModel?: string;

  /**
   * 单位
   */
  unit?: string;

  /**
   * 数量
   */
  quantity?: string;

  /**
   * 单价
   */
  unitPrice?: string;

  /**
   * 金额
   */
  amount?: string;

  /**
   * 税率
   */
  taxRate?: string;

  /**
   * 税额
   */
  taxAmount?: string;

  /**
   * 价税合计
   */
  totalAmount?: string;

  /**
   * 资金场景
   */
  financialScenario?: string;

  /**
   * 备注
   */
  remark?: string;
}

export interface CustomerInvoiceItemQuery extends PageQuery {
  /**
   * 客户ID
   */
  customerId?: number | string;

  /**
   * 老jsj发票明细ID
   */
  oldInvoiceDetailId?: number | string;

  /**
   * 发票ID
   */
  invoiceId?: number | string;

  /**
   * 税收分类编码
   */
  taxClassificationCode?: string;

  /**
   * 特定业务类型
   */
  specialBusinessType?: string;

  /**
   * 货物或应税劳务名称
   */
  goodsOrServiceName?: string;

  /**
   * 规格型号
   */
  specificationModel?: string;

  /**
   * 单位
   */
  unit?: string;

  /**
   * 数量
   */
  quantity?: string;

  /**
   * 单价
   */
  unitPrice?: string;

  /**
   * 金额
   */
  amount?: string;

  /**
   * 税率
   */
  taxRate?: string;

  /**
   * 税额
   */
  taxAmount?: string;

  /**
   * 价税合计
   */
  totalAmount?: string;

  /**
   * 资金场景
   */
  financialScenario?: string;

  /**
   * 日期范围参数
   */
  params?: any;
}
