import type { BaseEntity, PageQuery } from '#/api/common';

export interface BankReconciliationVO {
  /**
   * 主键ID
   */
  id: number | string;

  /**
   * 客户ID
   */
  customerId: number | string;

  /**
   * 账户余额
   */
  balance: number;

  /**
   * 银行名称
   */
  bankName: string;

  /**
   * 银行编码
   */
  bankCode: string;

  /**
   * 支出金额
   */
  expenditure: number;

  /**
   * 收入金额
   */
  income: number;

  /**
   * 银行系统记录的交易时间
   */
  tradingTime: string;

  /**
   * 对账单所属月份
   */
  month: number;

  /**
   * 对账单编号
   */
  recNum: string;

  /**
   * 交易摘要
   */
  summary: string;

  /**
   * 收支类型
   */
  type: string;

  /**
   * 往来单位
   */
  unit: string;

  /**
   * 凭证ID
   */
  voucherId: number | string;

  /**
   * 对账单所属年份
   */
  year: number;
}

export interface BankReconciliationForm extends BaseEntity {
  /**
   * 主键ID
   */
  id?: number | string;

  /**
   * 客户ID
   */
  customerId?: number | string;

  /**
   * 账户余额
   */
  balance?: number;

  /**
   * 银行名称
   */
  bankName?: string;

  /**
   * 银行编码
   */
  bankCode?: string;

  /**
   * 支出金额
   */
  expenditure?: number;

  /**
   * 收入金额
   */
  income?: number;

  /**
   * 银行系统记录的交易时间
   */
  tradingTime?: string;

  /**
   * 对账单所属月份
   */
  month?: number;

  /**
   * 对账单编号
   */
  recNum?: string;

  /**
   * 交易摘要
   */
  summary?: string;

  /**
   * 收支类型
   */
  type?: string;

  /**
   * 往来单位
   */
  unit?: string;

  /**
   * 对账单所属年份
   */
  year?: number;
}

export interface BankReconciliationQuery extends PageQuery {
  /**
   * 客户ID
   */
  customerId?: number | string;

  /**
   * 账户余额
   */
  balance?: number;

  /**
   * 银行名称
   */
  bankName?: string;

  /**
   * 银行编码
   */
  bankCode?: string;

  /**
   * 支出金额
   */
  expenditure?: number;

  /**
   * 收入金额
   */
  income?: number;

  /**
   * 银行系统记录的交易时间
   */
  tradingTime?: string;

  /**
   * 对账单所属月份
   */
  month?: number;

  /**
   * 对账单编号
   */
  recNum?: string;

  /**
   * 交易摘要
   */
  summary?: string;

  /**
   * 收支类型
   */
  type?: string;

  /**
   * 往来单位
   */
  unit?: string;

  /**
   * 对账单所属年份
   */
  year?: number;

  /**
   * 日期范围参数
   */
  params?: any;
}
