import type { BaseEntity, PageQuery } from '#/api/common';

export interface StockVO {
  /**
   *
   */
  id: number | string;

  /**
   * 客户ID
   */
  customerId: number | string;

  /**
   * 类型
   */
  type: string;

  /**
   * 单号
   */
  no: string;

  /**
   * 日期
   */
  stockDate: string;

  /**
   * 数量
   */
  num: number;

  /**
   * 价税合计
   */
  totalMoneyAmount: number;

  /**
   * 税额
   */
  taxMoneyAmount: number;

  /**
   * 金额
   */
  notTaxMoney: number;

  /**
   * 是否已经记账
   */
  isAccount: string;

  /**
   * 蓝字/红字
   */
  stockFontType: string;

  /**
   * 备注
   */
  remark: string;

  /**
   * 凭证ID
   */
  voucherId: number | string;

  /**
   * 记账日期
   */
  accountDate: string;
}

export interface StockForm extends BaseEntity {
  /**
   *
   */
  id?: number | string;

  /**
   * 客户ID
   */
  customerId?: number | string;

  /**
   * 类型
   */
  type?: string;

  /**
   * 单号
   */
  no?: string;

  /**
   * 日期
   */
  stockDate?: string;

  /**
   * 数量
   */
  num?: number;

  /**
   * 价税合计
   */
  totalMoneyAmount?: number;

  /**
   * 税额
   */
  taxMoneyAmount?: number;

  /**
   * 金额
   */
  notTaxMoney?: number;

  /**
   * 是否已经记账
   */
  isAccount?: string;

  /**
   * 蓝字/红字
   */
  stockFontType?: string;

  /**
   * 备注
   */
  remark?: string;

  /**
   * 凭证ID
   */
  voucherId?: number | string;

  /**
   * 记账日期
   */
  accountDate?: string;
}

export interface StockQuery extends PageQuery {
  /**
   * 客户ID
   */
  customerId?: number | string;

  /**
   * 类型
   */
  type?: string;

  /**
   * 单号
   */
  no?: string;

  /**
   * 日期
   */
  stockDate?: string;

  /**
   * 数量
   */
  num?: number;

  /**
   * 价税合计
   */
  totalMoneyAmount?: number;

  /**
   * 税额
   */
  taxMoneyAmount?: number;

  /**
   * 金额
   */
  notTaxMoney?: number;

  /**
   * 是否已经记账
   */
  isAccount?: string;

  /**
   * 蓝字/红字
   */
  stockFontType?: string;

  /**
   * 凭证ID
   */
  voucherId?: number | string;

  /**
   * 记账日期
   */
  accountDate?: string;

  /**
   * 日期范围参数
   */
  params?: any;
}
