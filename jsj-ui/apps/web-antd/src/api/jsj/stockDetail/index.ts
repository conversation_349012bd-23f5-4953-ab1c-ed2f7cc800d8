import type { StockDetailForm, StockDetailQuery, StockDetailVO } from './model';

import type { ID, IDS, PageResult } from '#/api/common';

import { commonExport } from '#/api/helper';
import { requestClient } from '#/api/request';

/**
 * 查询库存明细列表
 * @param params
 * @returns 库存明细列表
 */
export function stockDetailList(params?: StockDetailQuery) {
  return requestClient.get<PageResult<StockDetailVO>>('/jsj/stockDetail/list', {
    params,
  });
}

/**
 * 导出库存明细列表
 * @param params
 * @returns 库存明细列表
 */
export function stockDetailExport(params?: StockDetailQuery) {
  return commonExport('/jsj/stockDetail/export', params ?? {});
}

/**
 * 查询库存明细详情
 * @param id id
 * @returns 库存明细详情
 */
export function stockDetailInfo(id: ID) {
  return requestClient.get<StockDetailVO>(`/jsj/stockDetail/${id}`);
}

/**
 * 新增库存明细
 * @param data
 * @returns void
 */
export function stockDetailAdd(data: StockDetailForm) {
  return requestClient.postWithMsg<void>('/jsj/stockDetail', data);
}

/**
 * 更新库存明细
 * @param data
 * @returns void
 */
export function stockDetailUpdate(data: StockDetailForm) {
  return requestClient.putWithMsg<void>('/jsj/stockDetail', data);
}

/**
 * 删除库存明细
 * @param id id
 * @returns void
 */
export function stockDetailRemove(id: ID | IDS) {
  return requestClient.deleteWithMsg<void>(`/jsj/stockDetail/${id}`);
}
