import type { BaseEntity, PageQuery } from '#/api/common';

export interface AccountingEntriesVO {
  /**
   * ID
   */
  id: number | string;

  /**
   * 场景
   */
  scene: string;

  /**
   * 描述
   */
  description: string;

  /**
   * 凭证分录
   */
  voucherEntry: string;
}

export interface AccountingEntriesForm extends BaseEntity {
  /**
   * ID
   */
  id?: number | string;

  /**
   * 场景
   */
  scene?: string;

  /**
   * 描述
   */
  description?: string;

  /**
   * 凭证分录
   */
  voucherEntry?: string;
}

export interface AccountingEntriesQuery extends PageQuery {
  /**
   * 场景
   */
  scene?: string;

  /**
   * 描述
   */
  description?: string;

  /**
   * 凭证分录
   */
  voucherEntry?: string;

  /**
   * 日期范围参数
   */
  params?: any;
}
