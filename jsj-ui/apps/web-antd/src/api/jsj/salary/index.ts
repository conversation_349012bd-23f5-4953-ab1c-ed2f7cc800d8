import type { SalaryForm, SalaryQuery, SalaryVO } from './model';

import type { ID, IDS, PageResult } from '#/api/common';

import { commonExport } from '#/api/helper';
import { requestClient } from '#/api/request';

/**
 * 查询工资明细列表
 * @param params
 * @returns 工资明细列表
 */
export function salaryList(params?: SalaryQuery) {
  return requestClient.get<PageResult<SalaryVO>>('/jsj/salary/list', {
    params,
  });
}

/**
 * 导出工资明细列表
 * @param params
 * @returns 工资明细列表
 */
export function salaryExport(params?: SalaryQuery) {
  return commonExport('/jsj/salary/export', params ?? {});
}

/**
 * 查询工资明细详情
 * @param id id
 * @returns 工资明细详情
 */
export function salaryInfo(id: ID) {
  return requestClient.get<SalaryVO>(`/jsj/salary/${id}`);
}

/**
 * 新增工资明细
 * @param data
 * @returns void
 */
export function salaryAdd(data: SalaryForm) {
  return requestClient.postWithMsg<void>('/jsj/salary', data);
}

/**
 * 更新工资明细
 * @param data
 * @returns void
 */
export function salaryUpdate(data: SalaryForm) {
  return requestClient.putWithMsg<void>('/jsj/salary', data);
}

/**
 * 删除工资明细
 * @param id id
 * @returns void
 */
export function salaryRemove(id: ID | IDS) {
  return requestClient.deleteWithMsg<void>(`/jsj/salary/${id}`);
}
