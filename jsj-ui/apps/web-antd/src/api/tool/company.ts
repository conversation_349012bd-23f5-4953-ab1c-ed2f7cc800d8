import { RequestClient } from '@vben/request';

export interface UserCustomerNamesResponse {
  status: string;
  message: string;
  data: {
    data_source: string;
    saas_id: number;
    customer_names: string[];
  };
}

const client = new RequestClient({
  baseURL: import.meta.env.VITE_API_URL,
  isReturnNativeResponse: false,
  isTransformResponse: true,
});

/**
 * 获取用户负责的客户名称列表
 * @param username 登录账号
 * @param tenant_id 租户id
 * @returns Promise<UserCustomerNamesResponse> 返回客户名称列表数据
 */
export async function getUserCustomerNames({
  username,
  tenant_id,
}: {
  username: string;
  tenant_id: string;
}): Promise<UserCustomerNamesResponse> {
  return client.get<UserCustomerNamesResponse>(
    '/prod-api/autojob/api/users/get_user_info',
    {
      params: { username, tenant_id },
    }
  );
}
