import { RequestClient } from '@vben/request';

const client = new RequestClient({
  baseURL: import.meta.env.VITE_API_URL,
  isReturnNativeResponse: false,
  isTransformResponse: true,
});

// 凭证项目接口
export interface VoucherItem {
  source_type: string;
  source_info: {
    bank_receipt_info?: {
      expense_transaction_num: number;
      income_transaction_num: number;
      timestamp: string;
      total_expense_amount: number;
      total_income_amount: number;
    };
    invoice_info?: {
      amount: number;
      fund_desc: string;
      id: string[];
      tax: number;
      timestamp: string;
      total: number;
    };
    payroll_info?: {
      timestamp: string;
      total_employee_deductions: number;
      total_employer_contributions: number;
      total_gross_salary: number;
    };
  };
  executor: string;
  confirmed: boolean;
  voucher: {
    details: Array<{
      account: string;
      credit: number;
      debit: number;
      id: number;
      summary: string;
    }>;
    id: number;
    record_date: string;
    timestamp: string;
    total_credit: number;
    total_debit: number;
    type: string;
    unique_id: string;
  };
}

// API响应接口 - RequestClient会自动提取data字段
export interface VoucherResponse {
  company_name: string;
  items: VoucherItem[];
  month: string;
}

/**
 * 获取当前公司某个月份的凭证信息
 * @param company_name 公司名称
 * @param month 月份 (YYYYMM格式)
 * @returns 凭证信息
 */
export function getCurrentVouchers(company_name: string, month: string) {
  return client.get<VoucherResponse>('/prod-api/autojob/api/vouchers/current', {
    params: {
      company_name,
      month,
    },
  });
}

// 凭证原始数据接口 - RequestClient会自动提取data字段
export interface VoucherSourceDataResponse {
  company_name: string;
  month: string;
  voucher_unique_id: string;
  source_info: {
    bank_receipt?: Array<{
      account_name: string;
      account_number: string;
      amount: number;
      bank_name: string;
      conterpary_account_name: string;
      conterpary_account_number: string;
      conterpary_bank_name: string;
      currency: string;
      id: string;
      note: string;
      summary: string;
      transaction_id: string;
      transaction_time: string;
      type: string;
    }>;
    input_invoice?: Array<{
      amount: number;
      buyer_name: string;
      buyer_tax_id: string;
      description: string;
      digital_invoice_number?: string;
      goods_name: string;
      id: string;
      invoice_code: string;
      invoice_number: string;
      is_positive: string;
      issue_date: string;
      issuer: string;
      quantity: number;
      risk_level: string;
      seller_name: string;
      seller_tax_id: string;
      source: string;
      specific_biz_type?: string;
      specification: string;
      status: string;
      tax_amount: number;
      tax_classification_code: string;
      tax_rate: number;
      total_amount: number;
      type: string;
      unit: string;
      unit_price: number;
    }>;
    output_invoice?: Array<{
      amount: number;
      buyer_name: string;
      buyer_tax_id: string;
      description: string;
      digital_invoice_number?: string;
      goods_name: string;
      id: string;
      invoice_code: string;
      invoice_number: string;
      is_positive: string;
      issue_date: string;
      issuer: string;
      quantity: number;
      risk_level: string;
      seller_name: string;
      seller_tax_id: string;
      source: string;
      specific_biz_type?: string;
      specification: string;
      status: string;
      tax_amount: number;
      tax_classification_code: string;
      tax_rate: number;
      total_amount: number;
      type: string;
      unit: string;
      unit_price: number;
    }>;
    payroll_info?: Array<{
      actual_salary: number;
      base_salary: number;
      employee_id: string;
      extra_housing_fund: number;
      extra_medical: number;
      housing_fund_company: number;
      housing_fund_personal: number;
      id_number: string;
      medical_company: number;
      medical_personal: number;
      name: string;
      pension_company: number;
      pension_personal: number;
      performance_salary: number;
      position_salary: number;
      remark: string;
      taxable_salary: number;
      total_salary: number;
      unemployment_company: number;
      unemployment_personal: number;
    }>;
  };
}

/**
 * 获取凭证原始数据
 * @param company_name 公司名称
 * @param month 月份 (YYYYMM格式)
 * @param voucher_unique_id 凭证唯一ID
 * @returns 凭证原始数据
 */
export function getVoucherSourceData(
  company_name: string,
  month: string,
  voucher_unique_id: string,
) {
  return client.get<VoucherSourceDataResponse>(
    '/prod-api/autojob/api/vouchers/source-data',
    {
      params: {
        company_name,
        month,
        voucher_unique_id,
      },
    },
  );
}

// 修改凭证请求接口
export interface UpdateVoucherRequest {
  company_name: string;
  month: string;
  voucher_unique_id: string;
  voucher: {
    details: Array<{
      account: string;
      credit: number;
      debit: number;
      id: number;
      summary: string;
    }>;
    id: number;
    record_date: string;
    type: string;
  };
}

// 修改凭证响应接口
export interface UpdateVoucherResponse {
  result: string;
  timestamp: string;
  instance_id: string;
}

/**
 * 修改凭证信息
 * @param data 修改凭证请求数据
 * @returns 修改结果
 */
export function updateVoucher(data: UpdateVoucherRequest) {
  return client.put<UpdateVoucherResponse>(
    '/prod-api/autojob/api/vouchers/update',
    data,
  );
}

// 合并凭证请求接口
export interface MergeVouchersRequest {
  company_name: string;
  month: string;
  voucher_unique_ids: string[];
}

// 合并凭证响应接口
export interface MergeVouchersResponse {
  result: string;
  err_msg: string | null;
  merged_info: {
    source_type: string;
    source_info: {
      bank_receipt_info: {
        merged_voucher_count: number;
        original_voucher_ids: string[];
        merge_timestamp: string;
        total_transactions: number;
        total_amount: number;
      };
    };
    executor: string;
    confirmed: boolean;
    voucher: {
      unique_id: string;
      timestamp: string;
      id: number;
      type: string;
      record_date: string;
      total_debit: number;
      total_credit: number;
      details: Array<{
        id: number;
        summary: string;
        account: string;
        debit: number;
        credit: number;
      }>;
    };
  };
}

/**
 * 合并凭证
 * @param data 合并凭证请求数据
 * @returns 合并结果
 */
export function mergeVouchers(data: MergeVouchersRequest) {
  return client.post<MergeVouchersResponse>(
    '/prod-api/autojob/api/vouchers/merge',
    data,
  );
}
