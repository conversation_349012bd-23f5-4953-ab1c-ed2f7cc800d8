import { RequestClient } from '@vben/request';

/**
 * 业务数据状态接口定义
 */
export interface BusinessDataStatus {
  /** 状态 - 数据处理状态 */
  status: string;
  /** 数量 - 数据条数 */
  num: number;
  /** 详情数量 - 详细数据条数 */
  details_num?: number;
  /** 总金额 - 数据总金额 */
  total_amount: number;
  /** 凭证数量 - 生成的凭证数量 */
  voucher_num: number;
}

/**
 * 薪酬数据状态接口定义
 */
export interface PayrollStatus {
  /** 状态 - 薪酬处理状态 */
  status: string;
  /** 员工数量 - 薪酬涉及的员工数量 */
  empolyee_num: number;
  /** 总薪酬 - 薪酬总额 */
  total_salary: number;
  /** 个人社保公积金 - 个人承担部分 */
  total_sbhjz_person: number;
  /** 公司社保公积金 - 公司承担部分 */
  total_sbhjz_company: number;
  /** 凭证数量 - 生成的凭证数量 */
  voucher_num: number;
}

/**
 * 配置信息接口定义
 */
export interface CompanyConfigs {
  /** AI自动模式 - 自动处理模式配置
   * - "autojob": 开启自动模式
   * - "manual": 手动模式
   * - "none": 无配置
   */
  auto_mode: 'autojob' | 'manual' | 'none';
}

/**
 * 公司信息接口定义
 * 包含公司基本信息和各项业务状态
 */
export interface Company {
  /** 客户名称 - 公司的完整名称 */
  customerName: string;

  /** 客户编号 - 系统内部客户唯一标识 */
  customerCode?: string;

  /** 记账员 - 负责该公司记账的员工账号 */
  bookkeeper: string;

  /** 月份 - 数据所属月份，格式YYYYMM */
  month: number;

  /** 发票整理状态 - 理票进度状态 */
  invoiceArrangeStatus: string;

  /** 记账状态 - 会计记账处理状态 */
  accountStatus: string;

  /** 税务申报状态 - 税务申报处理状态 */
  taxDeclareStatus: string;

  /** 记账状态详情 - 记账处理的详细状态描述 */
  accountStatusDetail: string;

  /** 付款状态 - 税款缴纳状态 */
  paymentStatus: string;

  /** 清卡状态 - 税控设备清卡状态 */
  clearCardStatus: string;

  /** 抄税状态 - 税控设备抄税状态 */
  copyTaxStatus: string;

  /** 最新备注 - 该客户的最新处理备注信息 */
  latestNote: string | null;

  /** 置顶标志 - 是否为重要客户需要置顶显示 */
  topFlag: number;

  /** 销项发票 - 销项发票数据状态 */
  output_voice: BusinessDataStatus;

  /** 进项普票 - 进项普通发票数据状态 */
  input_voice_common: BusinessDataStatus;

  /** 进项专票 - 进项专用发票数据状态 */
  input_voice_vat: BusinessDataStatus;

  /** 银行回单 - 银行回单数据状态 */
  bank_receipt: BusinessDataStatus;

  /** 薪酬 - 薪酬数据状态 */
  payroll: PayrollStatus;

  /** 报关单 - 报关单数据状态 */
  customs_declaration_form: BusinessDataStatus;

  /** 配置信息 - 公司相关配置 */
  configs: CompanyConfigs;
}

/**
 * 公司列表API响应接口定义
 * 标准的API响应格式，包含状态和数据
 */
export interface CompaniesResponse {
  /** 响应状态 - API调用结果状态
   * - "success": 请求成功
   * - "error": 请求失败
   */
  status: string;

  /** 响应数据 - 包含公司列表和统计信息 */
  data: {
    /** 公司列表 - 包含所有公司信息的数组 */
    companies: Company[];

    /** 总数量 - 符合条件的公司总数 */
    total_count: number;

    /** 数据源 - 数据来源标识 */
    source?: string;

    /** 记账员 - 数据所属记账员 */
    bookkeeper?: string;

    /** 月份 - 数据所属月份 */
    month?: number;

    /** 刷新标志 - 是否为刷新数据 */
    refresh?: boolean;
  };
}

/**
 * Mock数据 - 用于开发和测试的模拟公司数据
 * 包含各种状态组合的示例数据
 */
const mockCompaniesData: CompaniesResponse = {
    "status": "success",
    "data": {
        "companies": [
            {
                "customerName": "青岛东软载波科技股份有限公司",
                "bookkeeper": "***********",
                "month": 202502,
                "invoiceArrangeStatus": "进行中",
                "accountStatus": "记账中",
                "taxDeclareStatus": "待申报",
                "accountStatusDetail": "进项普票:凭证已回写; 进项专票:数据已同步; 银行回单:凭证已生成; 工资单:数据已同步",
                "paymentStatus": "数据已同步",
                "clearCardStatus": "凭证已生成",
                "copyTaxStatus": "数据已同步",
                "latestNote": "更新时间: 2025-06-18 03:56:25.743000",
                "topFlag": 0,
                "output_voice": {
                    "status": "数据未同步",
                    "num": 16,
                    "details_num": 38,
                    "total_amount": 258292.43,
                    "voucher_num": 1
                },
                "input_voice_common": {
                    "status": "凭证已回写",
                    "num": 15,
                    "details_num": 13,
                    "total_amount": 124234.23,
                    "voucher_num": 1
                },
                "input_voice_vat": {
                    "status": "数据已同步",
                    "num": 39,
                    "details_num": 47,
                    "total_amount": 340852.07,
                    "voucher_num": 7
                },
                "bank_receipt": {
                    "status": "凭证已生成",
                    "num": 14,
                    "details_num": 28,
                    "total_amount": 176654.93,
                    "voucher_num": 1
                },
                "payroll": {
                    "status": "数据已同步",
                    "empolyee_num": 67,
                    "total_salary": 436229.23,
                    "total_sbhjz_person": 28130.3,
                    "total_sbhjz_company": 28582.74,
                    "voucher_num": 3
                },
                "customs_declaration_form": {
                    "status": "数据已同步",
                    "num": 12,
                    "total_amount": 122437.8,
                    "voucher_num": 0
                },
                "configs": {
                    "auto_mode": "autojob" //none
                }
            },
            {
                "customerName": "青岛华仁药业股份有限公司",
                "bookkeeper": "***********",
                "month": 202502,
                "invoiceArrangeStatus": "进行中",
                "accountStatus": "记账中",
                "taxDeclareStatus": "未开始",
                "accountStatusDetail": "销项发票:数据已同步; 进项普票:凭证已生成; 银行回单:数据已同步; 工资单:凭证已回写",
                "paymentStatus": "凭证已回写",
                "clearCardStatus": "数据已同步",
                "copyTaxStatus": "数据未同步",
                "latestNote": "更新时间: 2025-06-18 03:56:25.731000",
                "topFlag": 0,
                "output_voice": {
                    "status": "数据已同步",
                    "num": 17,
                    "details_num": 68,
                    "total_amount": 488627.55,
                    "voucher_num": 1
                },
                "input_voice_common": {
                    "status": "凭证已生成",
                    "num": 17,
                    "details_num": 81,
                    "total_amount": 133536.5,
                    "voucher_num": 6
                },
                "input_voice_vat": {
                    "status": "数据未同步",
                    "num": 19,
                    "details_num": 113,
                    "total_amount": 360958.06,
                    "voucher_num": 6
                },
                "bank_receipt": {
                    "status": "数据已同步",
                    "num": 13,
                    "details_num": 55,
                    "total_amount": 369442.99,
                    "voucher_num": 5
                },
                "payroll": {
                    "status": "凭证已回写",
                    "empolyee_num": 61,
                    "total_salary": 753206.72,
                    "total_sbhjz_person": 9288.2,
                    "total_sbhjz_company": 22777.3,
                    "voucher_num": 1
                },
                "customs_declaration_form": {
                    "status": "数据未同步",
                    "num": 19,
                    "total_amount": 57158.7,
                    "voucher_num": 1
                },
                "configs": {
                    "auto_mode": "autojob"
                }
            },
            {
                "customerName": "青岛双星股份有限公司",
                "bookkeeper": "***********",
                "month": 202502,
                "invoiceArrangeStatus": "进行中",
                "accountStatus": "记账中",
                "taxDeclareStatus": "待申报",
                "accountStatusDetail": "销项发票:凭证已生成; 进项普票:数据已同步; 进项专票:凭证已回写; 银行回单:凭证已生成; 工资单:数据已同步",
                "paymentStatus": "数据已同步",
                "clearCardStatus": "凭证已生成",
                "copyTaxStatus": "凭证已生成",
                "latestNote": "更新时间: 2025-06-18 03:56:25.690000",
                "topFlag": 0,
                "output_voice": {
                    "status": "凭证已生成",
                    "num": 37,
                    "details_num": 135,
                    "total_amount": 191599.09,
                    "voucher_num": 5
                },
                "input_voice_common": {
                    "status": "数据已同步",
                    "num": 12,
                    "details_num": 80,
                    "total_amount": 262991.73,
                    "voucher_num": 3
                },
                "input_voice_vat": {
                    "status": "凭证已回写",
                    "num": 15,
                    "details_num": 33,
                    "total_amount": 87644.29,
                    "voucher_num": 7
                },
                "bank_receipt": {
                    "status": "凭证已生成",
                    "num": 41,
                    "details_num": 44,
                    "total_amount": 458863.65,
                    "voucher_num": 6
                },
                "payroll": {
                    "status": "数据已同步",
                    "empolyee_num": 49,
                    "total_salary": 413135.02,
                    "total_sbhjz_person": 15168.75,
                    "total_sbhjz_company": 10391.66,
                    "voucher_num": 2
                },
                "customs_declaration_form": {
                    "status": "凭证已生成",
                    "num": 0,
                    "total_amount": 185344.06,
                    "voucher_num": 1
                },
                "configs": {
                    "auto_mode": "autojob"
                }
            },
            {
                "customerName": "青岛啤酒股份有限公司",
                "bookkeeper": "***********",
                "month": 202502,
                "invoiceArrangeStatus": "进行中",
                "accountStatus": "记账中",
                "taxDeclareStatus": "待申报",
                "accountStatusDetail": "销项发票:凭证已回写; 进项专票:数据已同步; 工资单:数据已同步",
                "paymentStatus": "数据已同步",
                "clearCardStatus": "数据未同步",
                "copyTaxStatus": "数据已同步",
                "latestNote": "更新时间: 2025-06-18 03:56:25.648000",
                "topFlag": 0,
                "output_voice": {
                    "status": "凭证已回写",
                    "num": 41,
                    "details_num": 82,
                    "total_amount": 52008.64,
                    "voucher_num": 9
                },
                "input_voice_common": {
                    "status": "数据未同步",
                    "num": 6,
                    "details_num": 53,
                    "total_amount": 51302.3,
                    "voucher_num": 4
                },
                "input_voice_vat": {
                    "status": "数据已同步",
                    "num": 40,
                    "details_num": 35,
                    "total_amount": 355342.63,
                    "voucher_num": 7
                },
                "bank_receipt": {
                    "status": "数据未同步",
                    "num": 38,
                    "details_num": 16,
                    "total_amount": 401620.2,
                    "voucher_num": 6
                },
                "payroll": {
                    "status": "数据已同步",
                    "empolyee_num": 193,
                    "total_salary": 157752.55,
                    "total_sbhjz_person": 21849.08,
                    "total_sbhjz_company": 29939.79,
                    "voucher_num": 2
                },
                "customs_declaration_form": {
                    "status": "数据已同步",
                    "num": 3,
                    "total_amount": 126479.6,
                    "voucher_num": 0
                },
                "configs": {
                    "auto_mode": "manual"
                }
            },
            {
                "customerName": "青岛天能重工股份有限公司",
                "bookkeeper": "***********",
                "month": 202502,
                "invoiceArrangeStatus": "已同步",
                "accountStatus": "记账中",
                "taxDeclareStatus": "待申报",
                "accountStatusDetail": "销项发票:数据已同步; 进项普票:数据已同步; 银行回单:凭证已生成; 工资单:凭证已生成",
                "paymentStatus": "凭证已生成",
                "clearCardStatus": "凭证已生成",
                "copyTaxStatus": "凭证已生成",
                "latestNote": "更新时间: 2025-06-18 03:56:25.755000",
                "topFlag": 0,
                "output_voice": {
                    "status": "数据已同步",
                    "num": 9,
                    "details_num": 100,
                    "total_amount": 172029.47,
                    "voucher_num": 8
                },
                "input_voice_common": {
                    "status": "数据已同步",
                    "num": 18,
                    "details_num": 36,
                    "total_amount": 53554.01,
                    "voucher_num": 7
                },
                "input_voice_vat": {
                    "status": "数据未同步",
                    "num": 24,
                    "details_num": 57,
                    "total_amount": 118662.2,
                    "voucher_num": 3
                },
                "bank_receipt": {
                    "status": "凭证已生成",
                    "num": 29,
                    "details_num": 36,
                    "total_amount": 384013.0,
                    "voucher_num": 2
                },
                "payroll": {
                    "status": "凭证已生成",
                    "empolyee_num": 28,
                    "total_salary": 437022.15,
                    "total_sbhjz_person": 32517.05,
                    "total_sbhjz_company": 32153.23,
                    "voucher_num": 1
                },
                "customs_declaration_form": {
                    "status": "凭证已生成",
                    "num": 15,
                    "total_amount": 88332.51,
                    "voucher_num": 4
                },
                "configs": {
                    "auto_mode": "manual"
                }
            },
            {
                "customerName": "青岛汉缆股份有限公司",
                "bookkeeper": "***********",
                "month": 202502,
                "invoiceArrangeStatus": "进行中",
                "accountStatus": "记账中",
                "taxDeclareStatus": "已申报",
                "accountStatusDetail": "销项发票:凭证已生成; 进项专票:数据已同步; 银行回单:数据已同步; 工资单:凭证已回写",
                "paymentStatus": "凭证已回写",
                "clearCardStatus": "数据已同步",
                "copyTaxStatus": "凭证已回写",
                "latestNote": "更新时间: 2025-06-18 03:56:25.768000",
                "topFlag": 0,
                "output_voice": {
                    "status": "凭证已生成",
                    "num": 27,
                    "details_num": 56,
                    "total_amount": 259620.92,
                    "voucher_num": 9
                },
                "input_voice_common": {
                    "status": "数据未同步",
                    "num": 16,
                    "details_num": 42,
                    "total_amount": 205320.62,
                    "voucher_num": 4
                },
                "input_voice_vat": {
                    "status": "数据已同步",
                    "num": 37,
                    "details_num": 94,
                    "total_amount": 207382.72,
                    "voucher_num": 12
                },
                "bank_receipt": {
                    "status": "数据已同步",
                    "num": 36,
                    "details_num": 29,
                    "total_amount": 588553.66,
                    "voucher_num": 3
                },
                "payroll": {
                    "status": "凭证已回写",
                    "empolyee_num": 39,
                    "total_salary": 150597.7,
                    "total_sbhjz_person": 19925.18,
                    "total_sbhjz_company": 15071.75,
                    "voucher_num": 1
                },
                "customs_declaration_form": {
                    "status": "凭证已回写",
                    "num": 8,
                    "total_amount": 33771.11,
                    "voucher_num": 0
                },
                "configs": {
                    "auto_mode": "autojob"
                }
            },
            {
                "customerName": "青岛海尔集团有限公司",
                "bookkeeper": "***********",
                "month": 202502,
                "invoiceArrangeStatus": "已同步",
                "accountStatus": "记账中",
                "taxDeclareStatus": "待申报",
                "accountStatusDetail": "进项专票:数据已同步; 银行回单:数据已同步; 工资单:凭证已生成",
                "paymentStatus": "凭证已生成",
                "clearCardStatus": "数据已同步",
                "copyTaxStatus": "数据已同步",
                "latestNote": "更新时间: 2025-06-18 03:56:25.636000",
                "topFlag": 0,
                "output_voice": {
                    "status": "数据未同步",
                    "num": 22,
                    "details_num": 50,
                    "total_amount": 331644.59,
                    "voucher_num": 9
                },
                "input_voice_common": {
                    "status": "数据未同步",
                    "num": 11,
                    "details_num": 28,
                    "total_amount": 276426.58,
                    "voucher_num": 4
                },
                "input_voice_vat": {
                    "status": "数据已同步",
                    "num": 24,
                    "details_num": 61,
                    "total_amount": 80221.62,
                    "voucher_num": 4
                },
                "bank_receipt": {
                    "status": "数据已同步",
                    "num": 12,
                    "details_num": 18,
                    "total_amount": 420211.11,
                    "voucher_num": 4
                },
                "payroll": {
                    "status": "凭证已生成",
                    "empolyee_num": 28,
                    "total_salary": 108390.83,
                    "total_sbhjz_person": 29922.61,
                    "total_sbhjz_company": 39990.88,
                    "voucher_num": 3
                },
                "customs_declaration_form": {
                    "status": "数据已同步",
                    "num": 1,
                    "total_amount": 77368.07,
                    "voucher_num": 0
                },
                "configs": {
                    "auto_mode": "manual"
                }
            },
            {
                "customerName": "青岛港国际股份有限公司",
                "bookkeeper": "***********",
                "month": 202502,
                "invoiceArrangeStatus": "进行中",
                "accountStatus": "记账中",
                "taxDeclareStatus": "未开始",
                "accountStatusDetail": "销项发票:凭证已回写; 进项普票:凭证已生成; 进项专票:凭证已回写; 工资单:凭证已生成",
                "paymentStatus": "凭证已生成",
                "clearCardStatus": "数据未同步",
                "copyTaxStatus": "数据未同步",
                "latestNote": "更新时间: 2025-06-18 03:56:25.661000",
                "topFlag": 0,
                "output_voice": {
                    "status": "凭证已回写",
                    "num": 22,
                    "details_num": 44,
                    "total_amount": 172511.22,
                    "voucher_num": 4
                },
                "input_voice_common": {
                    "status": "凭证已生成",
                    "num": 17,
                    "details_num": 61,
                    "total_amount": 112815.95,
                    "voucher_num": 7
                },
                "input_voice_vat": {
                    "status": "凭证已回写",
                    "num": 33,
                    "details_num": 69,
                    "total_amount": 126579.29,
                    "voucher_num": 4
                },
                "bank_receipt": {
                    "status": "数据未同步",
                    "num": 23,
                    "details_num": 32,
                    "total_amount": 517528.54,
                    "voucher_num": 3
                },
                "payroll": {
                    "status": "凭证已生成",
                    "empolyee_num": 90,
                    "total_salary": 334605.13,
                    "total_sbhjz_person": 12602.78,
                    "total_sbhjz_company": 20648.38,
                    "voucher_num": 2
                },
                "customs_declaration_form": {
                    "status": "数据未同步",
                    "num": 11,
                    "total_amount": 30276.07,
                    "voucher_num": 5
                },
                "configs": {
                    "auto_mode": "manual"
                }
            },
            {
                "customerName": "青岛软控股份有限公司",
                "bookkeeper": "***********",
                "month": 202502,
                "invoiceArrangeStatus": "进行中",
                "accountStatus": "记账中",
                "taxDeclareStatus": "待申报",
                "accountStatusDetail": "销项发票:凭证已生成; 进项普票:凭证已回写; 进项专票:凭证已回写",
                "paymentStatus": "数据未同步",
                "clearCardStatus": "数据未同步",
                "copyTaxStatus": "凭证已生成",
                "latestNote": "更新时间: 2025-06-18 03:56:25.709000",
                "topFlag": 0,
                "output_voice": {
                    "status": "凭证已生成",
                    "num": 44,
                    "details_num": 90,
                    "total_amount": 80865.46,
                    "voucher_num": 9
                },
                "input_voice_common": {
                    "status": "凭证已回写",
                    "num": 18,
                    "details_num": 16,
                    "total_amount": 232756.87,
                    "voucher_num": 4
                },
                "input_voice_vat": {
                    "status": "凭证已回写",
                    "num": 31,
                    "details_num": 35,
                    "total_amount": 226546.34,
                    "voucher_num": 5
                },
                "bank_receipt": {
                    "status": "数据未同步",
                    "num": 58,
                    "details_num": 11,
                    "total_amount": 289891.64,
                    "voucher_num": 1
                },
                "payroll": {
                    "status": "数据未同步",
                    "empolyee_num": 87,
                    "total_salary": 154188.18,
                    "total_sbhjz_person": 16330.36,
                    "total_sbhjz_company": 29181.97,
                    "voucher_num": 3
                },
                "customs_declaration_form": {
                    "status": "凭证已生成",
                    "num": 7,
                    "total_amount": 135802.81,
                    "voucher_num": 5
                },
                "configs": {
                    "auto_mode": "autojob"
                }
            },
            {
                "customerName": "青岛金王应用化学股份有限公司",
                "bookkeeper": "***********",
                "month": 202502,
                "invoiceArrangeStatus": "进行中",
                "accountStatus": "记账中",
                "taxDeclareStatus": "待申报",
                "accountStatusDetail": "进项普票:凭证已生成; 工资单:数据已同步",
                "paymentStatus": "数据已同步",
                "clearCardStatus": "数据未同步",
                "copyTaxStatus": "凭证已生成",
                "latestNote": "更新时间: 2025-06-18 03:56:25.720000",
                "topFlag": 0,
                "output_voice": {
                    "status": "数据未同步",
                    "num": 49,
                    "details_num": 125,
                    "total_amount": 310015.2,
                    "voucher_num": 4
                },
                "input_voice_common": {
                    "status": "凭证已生成",
                    "num": 29,
                    "details_num": 37,
                    "total_amount": 83147.19,
                    "voucher_num": 7
                },
                "input_voice_vat": {
                    "status": "数据未同步",
                    "num": 19,
                    "details_num": 120,
                    "total_amount": 164117.18,
                    "voucher_num": 11
                },
                "bank_receipt": {
                    "status": "数据未同步",
                    "num": 58,
                    "details_num": 40,
                    "total_amount": 282236.31,
                    "voucher_num": 6
                },
                "payroll": {
                    "status": "数据已同步",
                    "empolyee_num": 86,
                    "total_salary": 400403.32,
                    "total_sbhjz_person": 22075.64,
                    "total_sbhjz_company": 15072.39,
                    "voucher_num": 2
                },
                "customs_declaration_form": {
                    "status": "凭证已生成",
                    "num": 13,
                    "total_amount": 173960.32,
                    "voucher_num": 4
                },
                "configs": {
                    "auto_mode": "manual"
                }
            }
        ],
        "total_count": 10,
        "source": "accounting_summary",
        "bookkeeper": "***********",
        "month": 202502,
        "refresh": false
    }
}

/**
 * 获取公司列表参数接口
 */
export interface GetCompaniesListParams {
  /** 登录用户名 */
  account_name: string;
  /** 月份，格式YYYYMM */
  month?: string;
  /** 是否刷新数据 */
  refresh?: number;
}

/**
 * 获取公司列表 (Mock版本)
 * @param params 查询参数
 * @returns Promise<CompaniesResponse> 返回公司列表数据
 */
export async function getCompaniesList(params: GetCompaniesListParams): Promise<CompaniesResponse> {
  const requestClient = new RequestClient();

  return requestClient.get<CompaniesResponse>(
    '/prod-api/autojob/api/companies/list',
    {
      params,
    }
  );
}

/**
 * 更新AI自动模式参数接口
 */
export interface UpdateAutoModeParams {
  /** 公司名称 */
  company_name: string;
  /** AI自动模式 */
  auto_mode: 'autojob' | 'manual';
}

/**
 * 更新AI自动模式 (Mock版本)
 * @param params 更新参数
 * @returns Promise<{status: string}> 返回更新结果
 */
export async function updateAutoMode(params: UpdateAutoModeParams): Promise<{status: string}> {
  const requestClient = new RequestClient();

  return requestClient.post<{status: string}>(
    '/prod-api/autojob/api/companies/update-auto-mode',
    params
  );
}
