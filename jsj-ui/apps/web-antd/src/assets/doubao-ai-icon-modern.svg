<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 200 200" width="200" height="200">
  <!-- 渐变和滤镜定义 -->
  <defs>
    <radialGradient id="bgGradient" cx="50%" cy="50%" r="50%" fx="50%" fy="50%">
      <stop offset="0%" stop-color="#4F8DFF" />
      <stop offset="100%" stop-color="#0052D9" />
    </radialGradient>
    
    <filter id="glow" x="-20%" y="-20%" width="140%" height="140%">
      <feGaussianBlur stdDeviation="5" result="blur" />
      <feComposite in="SourceGraphic" in2="blur" operator="over" />
    </filter>
    
    <linearGradient id="beanGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#FFFFFF" />
      <stop offset="100%" stop-color="#F0F4FF" />
    </linearGradient>
    
    <clipPath id="beanClip">
      <path d="M100 45 C135 45, 155 70, 155 100 C155 135, 130 155, 100 155 C70 155, 45 130, 45 100 C45 70, 65 45, 100 45 Z" />
    </clipPath>
  </defs>
  
  <!-- 背景圆形 -->
  <circle cx="100" cy="100" r="90" fill="url(#bgGradient)" />
  
  <!-- 光晕效果 -->
  <circle cx="100" cy="100" r="85" fill="none" stroke="rgba(255,255,255,0.3)" stroke-width="2">
    <animate attributeName="r" values="85;90;85" dur="3s" repeatCount="indefinite" />
    <animate attributeName="opacity" values="0.3;0.6;0.3" dur="3s" repeatCount="indefinite" />
  </circle>
  
  <!-- 内部豆包形状 -->
  <path d="M100 45 C135 45, 155 70, 155 100 C155 135, 130 155, 100 155 C70 155, 45 130, 45 100 C45 70, 65 45, 100 45 Z" 
        fill="url(#beanGradient)" filter="url(#glow)" />
  
  <!-- 科技感纹理 -->
  <g clip-path="url(#beanClip)" opacity="0.1">
    <path d="M30 30 L170 170" stroke="#0052D9" stroke-width="0.5" />
    <path d="M30 170 L170 30" stroke="#0052D9" stroke-width="0.5" />
    <path d="M100 30 L100 170" stroke="#0052D9" stroke-width="0.5" />
    <path d="M30 100 L170 100" stroke="#0052D9" stroke-width="0.5" />
    
    <circle cx="100" cy="100" r="40" fill="none" stroke="#0052D9" stroke-width="0.5" />
    <circle cx="100" cy="100" r="60" fill="none" stroke="#0052D9" stroke-width="0.5" />
  </g>
  
  <!-- 豆包内部眼睛 - 更现代的设计 -->
  <g>
    <ellipse cx="80" cy="90" rx="12" ry="10" fill="#0052D9" />
    <circle cx="83" cy="87" r="4" fill="white" />
  </g>
  
  <g>
    <ellipse cx="120" cy="90" rx="12" ry="10" fill="#0052D9" />
    <circle cx="123" cy="87" r="4" fill="white" />
  </g>
  
  <!-- 豆包笑脸 - 更平滑的曲线 -->
  <path d="M75 115 Q100 140 125 115" stroke="#0052D9" stroke-width="6" stroke-linecap="round" fill="none" />
  
  <!-- 顶部光晕效果 -->
  <circle cx="75" cy="65" r="10" fill="white" opacity="0.6" />
  
  <!-- AI元素 - 更精细的电路图案 -->
  <g opacity="0.7">
    <!-- 左侧电路 -->
    <path d="M25 90 L45 90 L45 100" stroke="#FFFFFF" stroke-width="1.5" fill="none" />
    <circle cx="25" cy="90" r="2" fill="#FFFFFF" />
    
    <!-- 右侧电路 -->
    <path d="M175 90 L155 90 L155 100" stroke="#FFFFFF" stroke-width="1.5" fill="none" />
    <circle cx="175" cy="90" r="2" fill="#FFFFFF" />
    
    <!-- 顶部电路 -->
    <path d="M90 25 L90 45 L100 45" stroke="#FFFFFF" stroke-width="1.5" fill="none" />
    <circle cx="90" cy="25" r="2" fill="#FFFFFF" />
    
    <!-- 底部电路 -->
    <path d="M110 175 L110 155 L100 155" stroke="#FFFFFF" stroke-width="1.5" fill="none" />
    <circle cx="110" cy="175" r="2" fill="#FFFFFF" />
  </g>
  
  <!-- 脉冲动画效果 - 更细致 -->
  <circle cx="100" cy="100" r="95" stroke="#FFFFFF" stroke-width="1" fill="none" opacity="0.2">
    <animate attributeName="r" values="90;100;90" dur="4s" repeatCount="indefinite" />
    <animate attributeName="opacity" values="0.1;0.3;0.1" dur="4s" repeatCount="indefinite" />
  </circle>
  
  <!-- 额外的脉冲环 -->
  <circle cx="100" cy="100" r="80" stroke="#FFFFFF" stroke-width="0.5" fill="none" opacity="0.15">
    <animate attributeName="r" values="80;85;80" dur="3s" repeatCount="indefinite" />
    <animate attributeName="opacity" values="0.1;0.2;0.1" dur="3s" repeatCount="indefinite" />
  </circle>
</svg>
