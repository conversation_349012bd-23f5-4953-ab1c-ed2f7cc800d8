import { computed, ref } from 'vue';

import { defineStore } from 'pinia';

export interface ChatMessage {
  id: number;
  content: string;
  type:
    | 'ai'
    | 'bank_receipt_extracted' // 银行回单提取
    | 'salary_data_extracted' // 工资单数据提取
    | 'task_status' // 任务状态更新
    | 'user'
    | 'voucher_generation_completed'; // 凭证生成完成
  timestamp: Date;
  status?: 'error' | 'loading' | 'success';
  reference?: string;
  taskId?: string;
  // 直接在消息中包含ThoughtChain数据
  thoughtChainItems?: Array<{
    data?: any; // 添加数据字段，用于存储原始数据
    description?: string;
    extra?: any;
    status?: 'error' | 'pending' | 'success';
    timestamp?: Date; // 添加时间戳，用于排序和显示
    title: string;
  }>;
  messages?: Array<{
    content: string;
    timestamp: Date;
    type:
      | 'bank_receipt_extracted'
      | 'message'
      | 'salary_data_extracted'
      | 'task_status'
      | 'voucher_generation_completed';
  }>;
  attachments?: Array<{
    name: string;
    type: 'file' | 'image';
    url: string;
  }>;
  extraData?: {
    data?: {
      // 银行回单数据
      account_name?: string;
      account_number?: string;
      amount?: number;
      bank_name?: string;
      conversion_errors?: any[];
      // 工资单数据
      company_name?: string;
      employee_count?: number;
      employees?: Array<{
        employee_id: string;
        name: string;
        id_number: string;
        total_salary: number;
        base_salary: number;
        performance_salary: number;
        position_salary: number;
        taxable_salary: number;
        pension_personal: number;
        medical_personal: number;
        unemployment_personal: number;
        housing_fund_personal: number;
        pension_company: number;
        medical_company: number;
        unemployment_company: number;
        work_injury_company: number;
        housing_fund_company: number;
        extra_housing_fund: number;
        extra_medical: number;
        remark: string;
        actual_salary: number;
      }>;
      total_salary?: number;
      total_actual_salary?: number;
      // 凭证生成数据
      conversion_summary?: {
        balance_check: boolean;
        failed_conversions: number;
        success_rate: number;
        successful_conversions: number;
        total_credit_amount: number;
        total_debit_amount: number;
        total_records_processed: number;
        total_vouchers_generated: number;
      };
      currency?: string;
      generated_vouchers?: Array<{
        currency: string;
        date: string;
        description: string;
        details: Array<{
          account_code: string;
          account_name: string;
          credit: number;
          debit: number;
          summary: string;
        }>;
        source_data: {
          account_number: string;
          conversion_method: string;
          original_amount: number;
          transaction_id: string;
        };
        voucher_number: string;
        voucher_type: string;
      }>;
      metadata?: {
        conversion_time: number;
        total_processing_time: string;
        voucher_settings: {
          auto_balance: boolean;
          default_currency: string;
          rounding_precision: number;
          voucher_prefix: string;
        };
      };

      note?: string;
      summary?: string;
      transaction_time?: string;
      type?: string;
      validation_results?: {
        balance_issues: any[];
        invalid_vouchers: number;
        total_vouchers: number;
        valid_vouchers: number;
        validation_errors: any[];
      };
    };
    progress?: number;
    taskId?: string;
    thoughtChainItems?: Array<{
      description?: string;
      extra?: any;
      status?: 'error' | 'pending' | 'success';
      title: string;
    }>;
    type:
      | 'bank_receipt_extracted'
      | 'salary_data_extracted'
      | 'task_status'
      | 'voucher_generation_completed';
  };
}

export interface Conversation {
  id: string;
  title: string;
  messages: ChatMessage[];
  createdAt: Date;
  updatedAt: Date;
}

export const useAIChatStore = defineStore('ai-chat', () => {
  // 多对会话时的会话列表
  const conversations = ref<Conversation[]>([]);
  const currentConversationId = ref<null | string>(null);
  const isProcessing = ref(false);

  // Getters
  const currentConversation = computed(() =>
    conversations.value.find((conv) => conv.id === currentConversationId.value),
  );

  const currentMessages = computed(
    () => currentConversation.value?.messages || [],
  );

  // Actions
  function $reset() {
    conversations.value = [];
    currentConversationId.value = null;
    isProcessing.value = false;
  }

  function createConversation(title: string = '新对话') {
    const conversation: Conversation = {
      id: Date.now().toString(),
      title,
      messages: [],
      createdAt: new Date(),
      updatedAt: new Date(),
    };
    conversations.value.push(conversation);
    currentConversationId.value = conversation.id;
    return conversation;
  }

  function addMessage(message: Omit<ChatMessage, 'id' | 'timestamp'>) {
    if (!currentConversationId.value) {
      createConversation();
    }

    const newMessage: ChatMessage = {
      id: Date.now(),
      timestamp: new Date(),
      ...message,
    };

    const conversation = currentConversation.value;
    if (conversation) {
      conversation.messages.push(newMessage);
      conversation.updatedAt = new Date();
    }

    return newMessage;
  }

  async function sendMessage(content: string) {
    if (!content.trim()) return;

    addMessage({
      content: content.trim(),
      type: 'user',
      status: 'success',
    });

    const aiMessage = addMessage({
      content: '',
      type: 'ai',
      status: 'loading',
    });

    try {
      isProcessing.value = true;
      // TODO: 调用实际的 AI API  wssend
      const response = await mockAIResponse(content);

      aiMessage.content = response;
      aiMessage.status = 'success';
    } catch {
      aiMessage.status = 'error';
      aiMessage.content = '抱歉，处理您的请求时出现错误。';
    } finally {
      isProcessing.value = false;
    }
  }

  function clearCurrentConversation() {
    if (currentConversationId.value) {
      const index = conversations.value.findIndex(
        (conv) => conv.id === currentConversationId.value,
      );
      if (index !== -1) {
        conversations.value.splice(index, 1);
      }
      currentConversationId.value = null;
    }
  }

  // 模拟 AI 响应
  async function mockAIResponse(content: string): Promise<string> {
    await new Promise((resolve) => setTimeout(resolve, 1000));
    return `这是对 "${content}" 的模拟回复。在实际应用中，这里应该调用真实的AI API。`;
  }

  function findMessageByTaskId(taskId: string, type: string) {
    return currentMessages.value.find((msg) => msg.taskId === taskId && msg.type === type);
  }

  function updateMessageByTaskId(taskId: string, type: string, update: Partial<ChatMessage>) {
    const message = findMessageByTaskId(taskId, type);
    if (message) {
      // 处理子消息
      if (!message.messages) {
        message.messages = [];
      }
      message.messages.push({
        content: update.content || '',
        timestamp: new Date(),
        type: update.extraData?.type || 'message',
      });

      // 特殊处理thoughtChainItems，确保不会覆盖现有的items
      if (update.thoughtChainItems) {
        // 如果update中提供了thoughtChainItems，使用它
        message.thoughtChainItems = update.thoughtChainItems;
        // 从update中移除thoughtChainItems，防止下面的Object.assign覆盖
        delete update.thoughtChainItems;
      }

      // 更新其他属性
      Object.assign(message, update);
    }
    return message;
  }

  return {
    // 状态
    conversations,
    currentConversationId,
    isProcessing,

    // Getters
    currentConversation,
    currentMessages,

    // Actions
    createConversation,
    addMessage,
    sendMessage,
    clearCurrentConversation,
    $reset,
    findMessageByTaskId,
    updateMessageByTaskId,
  };
});
