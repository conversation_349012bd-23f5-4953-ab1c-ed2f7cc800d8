<script setup lang="ts">
  import type { ChatMessage } from '#/store/modules/ai-chat';

  import { computed, h } from 'vue';

  import { Button, Descriptions, Modal, Table } from 'ant-design-vue';

  interface Props {
    message: ChatMessage | null;
    visible: boolean;
  }

  interface Emits {
    (e: 'update:visible', value: boolean): void;
  }

  const props = defineProps<Props>();
  const emit = defineEmits<Emits>();

  const handleClose = () => {
    emit('update:visible', false);
  };

  const modalTitle = computed(() => {
    if (!props.message) return '';

    switch (props.message.type) {
      case 'bank_receipt_extracted': {
        return '银行回单详情';
      }
      case 'salary_data_extracted': {
        return '工资单详情';
      }
      case 'task_status': {
        return '任务进度详情';
      }
      case 'voucher_generation_completed': {
        return '凭证生成详情';
      }
      default: {
        return '消息详情';
      }
    }
  });

  // 银行回单详情渲染
  const renderBankReceiptDetail = (data: any) => {
    return h(
      Descriptions,
      {
        bordered: true,
        column: 1,
        size: 'small',
        title: '银行回单信息',
      },
      {
        default: () =>
          [
            h(
              Descriptions.Item,
              { label: '交易时间' },
              () => data.transaction_time,
            ),
            h(Descriptions.Item, { label: '账号' }, () => data.account_number),
            h(Descriptions.Item, { label: '账户名' }, () => data.account_name),
            h(Descriptions.Item, { label: '开户行' }, () => data.bank_name),
            h(Descriptions.Item, { label: '交易类型' }, () => data.type),
            h(
              Descriptions.Item,
              { label: '金额' },
              () => `${data.amount} ${data.currency}`,
            ),
            h(Descriptions.Item, { label: '摘要' }, () => data.summary),
            data.note &&
              h(Descriptions.Item, { label: '备注' }, () => data.note),
          ].filter(Boolean),
      },
    );
  };

  // 工资单详情渲染
  const renderSalaryDetail = (data: any) => {
    const summaryComponent = h(
      Descriptions,
      {
        bordered: true,
        column: 2,
        size: 'small',
        style: { marginBottom: '16px' },
        title: '工资单汇总',
      },
      {
        default: () => [
          h(Descriptions.Item, { label: '公司名称' }, () => data.company_name),
          h(
            Descriptions.Item,
            { label: '员工人数' },
            () => data.employee_count,
          ),
          h(
            Descriptions.Item,
            { label: '工资总额' },
            () => `¥${data.total_salary?.toLocaleString() || 0}`,
          ),
          h(
            Descriptions.Item,
            { label: '实发工资总额' },
            () => `¥${data.total_actual_salary?.toLocaleString() || 0}`,
          ),
        ],
      },
    );

    const employeeColumns = [
      {
        dataIndex: 'employee_id',
        key: 'employee_id',
        title: '员工编号',
        width: 100,
      },
      { dataIndex: 'name', key: 'name', title: '姓名', width: 100 },
      {
        dataIndex: 'id_number',
        key: 'id_number',
        title: '身份证号',
        width: 150,
      },
      {
        customRender: ({ text }: { text: number }) =>
          `¥${text?.toLocaleString() || 0}`,
        dataIndex: 'total_salary',
        key: 'total_salary',
        title: '应发工资',
        width: 120,
      },
      {
        customRender: ({ text }: { text: number }) =>
          `¥${text?.toLocaleString() || 0}`,
        dataIndex: 'actual_salary',
        key: 'actual_salary',
        title: '实发工资',
        width: 120,
      },
      { dataIndex: 'remark', key: 'remark', title: '备注' },
    ];

    const employeeTable =
      data.employees && data.employees.length > 0
        ? h(Table, {
            columns: employeeColumns,
            dataSource: data.employees,
            pagination: { pageSize: 10 },
            rowKey: 'employee_id',
            scroll: { x: 700 },
            size: 'small',
            title: () => '员工工资明细',
          })
        : null;

    return h('div', [summaryComponent, employeeTable]);
  };

  // 凭证生成详情渲染
  const renderVoucherDetail = (data: any) => {
    const summary = data.conversion_summary;

    const summaryComponent = h(
      Descriptions,
      {
        bordered: true,
        column: 2,
        size: 'small',
        style: { marginBottom: '16px' },
        title: '转换摘要',
      },
      {
        default: () => [
          h(
            Descriptions.Item,
            { label: '处理记录总数' },
            () => summary.total_records_processed,
          ),
          h(
            Descriptions.Item,
            { label: '成功转换数' },
            () => summary.successful_conversions,
          ),
          h(
            Descriptions.Item,
            { label: '失败转换数' },
            () => summary.failed_conversions,
          ),
          h(
            Descriptions.Item,
            { label: '成功率' },
            () => `${summary.success_rate}%`,
          ),
          h(
            Descriptions.Item,
            { label: '生成凭证总数' },
            () => summary.total_vouchers_generated,
          ),
          h(
            Descriptions.Item,
            { label: '借方总金额' },
            () => summary.total_debit_amount,
          ),
          h(
            Descriptions.Item,
            { label: '贷方总金额' },
            () => summary.total_credit_amount,
          ),
          h(Descriptions.Item, { label: '平衡检查' }, () =>
            summary.balance_check ? '通过' : '未通过',
          ),
        ],
      },
    );

    // 凭证列表
    const voucherComponents = [];
    if (data.generated_vouchers && data.generated_vouchers.length > 0) {
      data.generated_vouchers.forEach((voucher: any, index: number) => {
        const voucherInfo = h(
          Descriptions,
          {
            bordered: true,
            column: 2,
            size: 'small',
            style: { marginBottom: '12px' },
            title: `凭证 ${index + 1} - ${voucher.voucher_number}`,
          },
          {
            default: () => [
              h(
                Descriptions.Item,
                { label: '凭证类型' },
                () => voucher.voucher_type,
              ),
              h(
                Descriptions.Item,
                { label: '日期' },
                () => voucher.date || '无',
              ),
              h(
                Descriptions.Item,
                { label: '描述', span: 2 },
                () => voucher.description,
              ),
            ],
          },
        );

        const detailTable = h(Table, {
          columns: [
            {
              dataIndex: 'account_code',
              key: 'account_code',
              title: '科目代码',
              width: 100,
            },
            {
              dataIndex: 'account_name',
              key: 'account_name',
              title: '科目名称',
              width: 120,
            },
            {
              dataIndex: 'summary',
              ellipsis: true,
              key: 'summary',
              title: '摘要',
            },
            {
              align: 'right',
              dataIndex: 'debit',
              key: 'debit',
              title: '借方',
              width: 100,
            },
            {
              align: 'right',
              dataIndex: 'credit',
              key: 'credit',
              title: '贷方',
              width: 100,
            },
          ],
          dataSource: voucher.details,
          pagination: false,
          rowKey: (_record: any, idx: number) => `${index}-${idx}`,
          size: 'small',
          style: { marginBottom: '16px' },
        });

        voucherComponents.push(voucherInfo, detailTable);
      });
    }

    return h('div', [summaryComponent, ...voucherComponents]);
  };

  // 任务状态详情渲染
  const renderTaskStatusDetail = (message: any) => {
    if (!message.thoughtChainItems || message.thoughtChainItems.length === 0) {
      return h('div', '暂无任务步骤信息');
    }

    const totalSteps = message.thoughtChainItems.length;
    // 检查最后一个步骤的状态来判断任务是否完成
    const lastStep = message.thoughtChainItems[totalSteps - 1];
    const isTaskCompleted =
      lastStep?.data?.status === 'completed' || lastStep?.status === 'success';
    // 如果任务已完成，所有步骤都算成功；否则除了最后一个步骤，前面的都算成功
    const completedSteps = isTaskCompleted
      ? totalSteps
      : (totalSteps > 0
        ? totalSteps - 1
        : 0);
    const progressPercent =
      totalSteps > 0 ? Math.round((completedSteps / totalSteps) * 100) : 0;

    // 进度摘要
    const progressSummary = h(
      Descriptions,
      {
        bordered: true,
        column: 2,
        size: 'small',
        style: { marginBottom: '16px' },
        title: '任务进度摘要',
      },
      {
        default: () => [
          h(Descriptions.Item, { label: '任务名称' }, () => message.content),
          h(Descriptions.Item, { label: '总步骤数' }, () => totalSteps),
          h(Descriptions.Item, { label: '已完成步骤' }, () => completedSteps),
          h(
            Descriptions.Item,
            { label: '完成进度' },
            () => `${progressPercent}%`,
          ),
        ],
      },
    );

    // 详细步骤表格
    const stepColumns = [
      { dataIndex: 'title', key: 'title', title: '步骤名称', width: 200 },
      {
        dataIndex: 'description',
        ellipsis: true,
        key: 'description',
        title: '描述',
      },
      {
        customRender: ({ record, text }: { record: any; text: string }) => {
          // 修正状态逻辑：根据任务完成状态和步骤位置确定状态
          const stepIndex = message.thoughtChainItems.indexOf(record);
          const isLastStep = stepIndex === message.thoughtChainItems.length - 1;
          const actualStatus = isTaskCompleted
            ? 'success'
            : isLastStep
              ? 'pending'
              : 'success';

          const statusMap = {
            default: { color: '#999', text: '等待中' },
            error: { color: '#ff4d4f', text: '失败' },
            pending: { color: '#1890ff', text: '进行中' },
            success: { color: '#52c41a', text: '已完成' },
          };
          const status =
            statusMap[actualStatus as keyof typeof statusMap] ||
            statusMap.default;
          return h(
            'span',
            { style: { color: status.color, fontWeight: '500' } },
            status.text,
          );
        },
        dataIndex: 'status',
        key: 'status',
        title: '状态',
        width: 100,
      },
      {
        customRender: ({ text }: { text: number }) => {
          if (!text) return '-';
          return new Date(text).toLocaleString('zh-CN');
        },
        dataIndex: 'timestamp',
        key: 'timestamp',
        title: '时间',
        width: 150,
      },
    ];

    const stepTable = h(Table, {
      columns: stepColumns,
      dataSource: message.thoughtChainItems,
      pagination: { pageSize: 20 },
      rowKey: (_record: any, index: number) => `step-${index}`,
      size: 'small',
      title: () => '详细步骤列表',
    });

    return h('div', [progressSummary, stepTable]);
  };

  const modalContent = computed(() => {
    if (!props.message) {
      return h('div', '暂无消息信息');
    }

    // 任务状态类型不需要extraData
    if (props.message.type === 'task_status') {
      return renderTaskStatusDetail(props.message);
    }

    // 其他类型需要extraData
    if (!props.message.extraData?.data) {
      return h('div', '暂无详细信息');
    }

    const data = props.message.extraData.data;

    switch (props.message.type) {
      case 'bank_receipt_extracted': {
        return renderBankReceiptDetail(data);
      }
      case 'salary_data_extracted': {
        return renderSalaryDetail(data);
      }
      case 'voucher_generation_completed': {
        return renderVoucherDetail(data);
      }
      default: {
        return h('div', props.message.content);
      }
    }
  });
</script>

<template>
  <Modal :open="visible" :title="modalTitle" width="80%" @cancel="handleClose">
    <div class="message-detail-content">
      <component :is="modalContent" />
    </div>

    <template #footer>
      <Button @click="handleClose">关闭</Button>
    </template>
  </Modal>
</template>

<style scoped lang="scss">
  .message-detail-content {
    max-height: 70vh;
    overflow-y: auto;

    :deep(.ant-descriptions) {
      margin-bottom: 16px;
    }

    :deep(.ant-table) {
      margin-bottom: 16px;
    }

    :deep(.ant-descriptions-title) {
      font-weight: 600;
      color: #1890ff;
    }
  }
</style>
