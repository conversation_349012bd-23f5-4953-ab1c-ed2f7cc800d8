<script setup lang="ts">
import { h, ref } from 'vue';
import { CloudUploadOutlined } from '@ant-design/icons-vue';
import { Attachments, Sender } from 'ant-design-x-vue';
import type { UploadFile } from 'ant-design-vue/es/upload/interface';
import type { UploadedFile } from '../types/chat';

interface Props {
  isOpen: boolean;
  items: UploadFile[];
  loading?: boolean;
}

interface Emits {
  (e: 'update:isOpen', value: boolean): void;
  (e: 'file-change', data: { fileList: UploadFile[] }): void;
  (e: 'file-upload', firstFile: File, fileList: FileList): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const attachmentsRef = ref<InstanceType<typeof Attachments> | null>(null);

const handleOpenChange = (open: boolean) => {
  emit('update:isOpen', open);
};

const handleFileChange = (data: { fileList: UploadFile[] }) => {
  emit('file-change', data);
};

const handleFileUpload = (firstFile: File, fileList: FileList) => {
  emit('file-upload', firstFile, fileList);
};

const getDropContainer = () => {
  // 返回一个稳定的拖拽容器
  return document.body;
};

const placeholder = (type: string) => {
  return type === 'drop'
    ? {
        title: 'Drop file here',
      }
    : {
        description: 'Click or drag files to this area to upload',
        icon: h(CloudUploadOutlined),
        title: '上传文件',
      };
};

defineExpose({
  attachmentsRef,
});
</script>

<template>
  <Sender.Header
    title="附件"
    :styles="{
      content: {
        padding: 0,
      },
    }"
    :open="isOpen"
    :on-open-change="handleOpenChange"
    force-render
  >
    <Attachments
      ref="attachmentsRef"
      :before-upload="() => false"
      :items="items"
      :on-change="handleFileChange"
      :placeholder="placeholder"
      :get-drop-container="getDropContainer"
    />
  </Sender.Header>
</template>

<style scoped lang="scss">
:deep(.ant-sender-header) {
  background: #fafafa;
  border-bottom: 1px solid #f0f0f0;
  border-radius: 8px 8px 0 0;

  .ant-sender-header-title {
    font-size: 13px;
    font-weight: 500;
    color: #333;
  }

  .ant-sender-header-extra {
    .ant-btn {
      color: #8c8c8c;
      background: transparent;
      border: none;

      &:hover {
        color: #1890ff;
        background: #f0f2f5;
      }
    }
  }
}

:deep(.ant-attachments) {
  padding: 12px;
  background: white;
  border-radius: 0 0 8px 8px;

  .ant-attachments-upload {
    background: #fafafa;
    border: 2px dashed #d9d9d9;
    border-radius: 6px;
    transition: all 0.3s ease;

    &:hover {
      background: #f0f8ff;
      border-color: #1890ff;
    }

    .ant-attachments-upload-placeholder {
      padding: 16px;
      text-align: center;

      .ant-attachments-upload-icon {
        margin-bottom: 8px;
        font-size: 24px;
        color: #8c8c8c;
      }

      .ant-attachments-upload-title {
        margin-bottom: 4px;
        font-size: 14px;
        font-weight: 500;
        color: #333;
      }

      .ant-attachments-upload-description {
        font-size: 12px;
        color: #8c8c8c;
      }
    }
  }

  .ant-attachments-list {
    margin-top: 8px;

    .ant-attachments-item {
      padding: 8px;
      margin-bottom: 4px;
      background: #f8f9fa;
      border: 1px solid #e9ecef;
      border-radius: 4px;
      transition: all 0.2s ease;

      &:hover {
        background: #e6f7ff;
        border-color: #1890ff;
      }

      .ant-attachments-item-content {
        display: flex;
        gap: 8px;
        align-items: center;

        .ant-attachments-item-icon {
          font-size: 16px;
          color: #1890ff;
        }

        .ant-attachments-item-name {
          flex: 1;
          overflow: hidden;
          font-size: 13px;
          color: #333;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .ant-attachments-item-size {
          font-size: 11px;
          color: #8c8c8c;
        }

        .ant-attachments-item-actions {
          .ant-btn {
            width: 20px;
            height: 20px;
            padding: 0;
            color: #8c8c8c;
            background: transparent;
            border: none;

            &:hover {
              color: #ff4d4f;
              background: #fff2f0;
            }
          }
        }
      }

      .ant-attachments-item-progress {
        margin-top: 4px;

        .ant-progress {
          .ant-progress-bg {
            height: 2px;
          }
        }
      }
    }
  }
}
</style>
