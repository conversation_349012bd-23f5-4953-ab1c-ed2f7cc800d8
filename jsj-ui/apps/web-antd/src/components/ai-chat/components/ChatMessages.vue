<script setup lang="ts">
  import type { BubbleListProps } from 'ant-design-x-vue';

  import type { ChatMessage } from '#/store/modules/ai-chat';

  import { computed, h } from 'vue';

  import { RobotOutlined, UserOutlined } from '@ant-design/icons-vue';
  import { Button, Flex } from 'ant-design-vue';
  import { Attachments, Bubble, Prompts, Welcome } from 'ant-design-x-vue';

  import MessageBubble from './MessageBubble.vue';

  interface Props {
    initializationError: null | string;
    isInitializing: boolean;
    loading: boolean;
    messages: ChatMessage[];
  }

  interface Emits {
    (e: 'showMessageDetail', message: ChatMessage): void;
    (e: 'reloadPage'): void;
  }

  const props = defineProps<Props>();
  const emit = defineEmits<Emits>();

  const showMessageDetail = (message: ChatMessage) => {
    emit('showMessageDetail', message);
  };

  const reloadPage = () => {
    emit('reloadPage');
  };

  const formatTime = (date: Date) => {
    return new Intl.DateTimeFormat('zh-CN', {
      hour: '2-digit',
      minute: '2-digit',
    }).format(date);
  };

  const bubbleItems = computed(() =>
    props.messages.map((msg) => {
      const lastMessage = props.messages[props.messages.length - 1];
      const isLoading =
        (msg.type === 'ai' ||
          msg.type === 'bank_receipt_extracted' ||
          msg.type === 'salary_data_extracted') &&
        lastMessage &&
        lastMessage.id === msg.id &&
        props.loading;

      return {
        content: h(MessageBubble, {
          isLoading,
          message: msg,
          onShowDetail: showMessageDetail,
        }),
        key: msg.id,
        loading: isLoading,
        meta: formatTime(msg.timestamp),
        role: msg.type,
        shape: 'corner' as const,
        status: msg.status,
        type: msg.type,
        typing: msg.type === 'ai' ? { interval: 50, step: 2 } : undefined,
      };
    }),
  );

  const roles: BubbleListProps['roles'] = {
    ai: {
      avatar: { icon: h(RobotOutlined), style: { background: '#1890ff' } },
      placement: 'start',
      typing: true,
    },
    bank_receipt_extracted: {
      avatar: { icon: h(RobotOutlined), style: { background: '#1890ff' } },
      placement: 'start',
      typing: true,
    },
    file: {
      avatar: { icon: h(UserOutlined), style: { visibility: 'hidden' } },
      messageRender: (items: any) =>
        h(
          Flex,
          { gap: 'middle', vertical: true },
          items.map((item: any) =>
            h(Attachments.FileCard, { item, key: item.uid }),
          ),
        ),
      placement: 'start',
      variant: 'borderless',
    },
    image: {
      avatar: { icon: h(UserOutlined), style: { visibility: 'hidden' } },
      messageRender: (items: any) =>
        h(
          'div',
          { style: { display: 'flex', flexDirection: 'column', gap: '8px' } },
          items.map((item: any) =>
            h('img', {
              alt: item.name,
              key: item.uid,
              src: item.url,
              style: { borderRadius: '4px', maxWidth: '300px' },
            }),
          ),
        ),
      placement: 'start',
      variant: 'borderless',
    },
    salary_data_extracted: {
      avatar: { icon: h(RobotOutlined), style: { background: '#1890ff' } },
      placement: 'start',
      typing: true,
    },
    suggestion: {
      avatar: { icon: h(UserOutlined), style: { visibility: 'hidden' } },
      messageRender: (items) =>
        h(Prompts, { vertical: true, items: items as any }),
      placement: 'start',
      variant: 'borderless',
    },
    task_status: {
      avatar: { icon: h(RobotOutlined), style: { background: '#1890ff' } },
      placement: 'start',
      typing: true,
    },
    user: {
      avatar: { icon: h(UserOutlined), style: { background: '#52c41a' } },
      placement: 'end',
    },
    voucher_generation_completed: {
      avatar: { icon: h(RobotOutlined), style: { background: '#1890ff' } },
      placement: 'start',
      typing: true,
    },
  };
</script>

<template>
  <div class="chat-messages">
    <!-- 初始化加载状态 -->
    <div v-if="isInitializing" class="loading-container">
      <div class="loading-content">
        <div class="loading-spinner"></div>
        <p class="loading-text">正在初始化AI助手...</p>
      </div>
    </div>

    <!-- 初始化错误状态 -->
    <div v-else-if="initializationError" class="error-container">
      <div class="error-content">
        <div class="error-icon">⚠️</div>
        <p class="error-text">{{ initializationError }}</p>
        <Button type="primary" @click="reloadPage">重新加载</Button>
      </div>
    </div>

    <!-- 正常状态 -->
    <div v-else-if="messages.length === 0" class="welcome-container">
      <Welcome
        :class-names="{
          title: 'welcome-title',
          description: 'welcome-description',
        }"
        icon="https://cdn-icons-png.flaticon.com/128/8943/8943377.png"
      >
        <template #title>精算家AI会计助手</template>
        <template #description>
          <div class="welcome-desc-content">
            <p class="main-desc">专业的AI助手，帮您快速生成准确的会计凭证</p>
            <p class="sub-desc">上传财务文件，即可自动识别并生成凭证</p>
          </div>
        </template>
      </Welcome>
    </div>

    <Bubble.List
      v-else
      :items="bubbleItems"
      :roles="roles"
      :style="{ padding: '8px' }"
      class="bubble-list"
    />
  </div>
</template>

<style scoped lang="scss">
  .chat-messages {
    height: 100%;
    overflow-y: auto;
  }

  .loading-container,
  .error-container {
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    height: 100% !important;
    padding: 32px !important;
  }

  .loading-content,
  .error-content {
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
    justify-content: center !important;
    text-align: center !important;
  }

  .loading-spinner {
    width: 32px !important;
    height: 32px !important;
    margin-bottom: 16px !important;
    border: 3px solid #f3f3f3 !important;
    border-top: 3px solid #667eea !important;
    border-radius: 50% !important;
    animation: spin 1s linear infinite !important;
  }

  .loading-text {
    margin: 0 !important;
    font-size: 14px !important;
    color: #666 !important;
  }

  .error-icon {
    margin-bottom: 16px !important;
    font-size: 32px !important;
  }

  .error-text {
    margin: 0 0 16px !important;
    font-size: 14px !important;
    color: #ff4d4f !important;
  }

  .welcome-container {
    padding: 16px !important;

    :deep(.welcome-title) {
      // margin-bottom: 10px !important;
      font-size: 16px !important;
      font-weight: 600 !important;
      color: #2d3748 !important;
      // text-align: center !important;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
      background-clip: text !important;
      -webkit-text-fill-color: transparent !important;
    }

    :deep(.welcome-description) {
      font-size: 12px !important;
      line-height: 1.5 !important;
      color: #4a5568 !important;
      // text-align: center !important;

      .welcome-desc-content {
        .main-desc {
          margin-bottom: 6px !important;
          font-size: 13px !important;
          font-weight: 500 !important;
          line-height: 1.4 !important;
          color: #333 !important;
        }

        .sub-desc {
          margin: 0 !important;
          font-size: 12px !important;
          color: #666 !important;
        }
      }
    }
  }

  .bubble-list {
    :deep(.ant-welcome) {
      padding: 20px !important;
      text-align: center !important;
      background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%) !important;
      border: 1px solid rgb(255 255 255 / 60%) !important;
      border-radius: 12px !important;
      box-shadow: 0 4px 16px rgb(0 0 0 / 8%) !important;
    }

    :deep(.ant-welcome-icon) {
      display: flex !important;
      align-items: center !important;
      justify-content: center !important;
      width: 40px !important;
      height: 40px !important;
      margin-bottom: 12px !important;
    }
  }

  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }

    100% {
      transform: rotate(360deg);
    }
  }
</style>
