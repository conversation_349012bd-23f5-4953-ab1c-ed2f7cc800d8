<script setup lang="ts">
  import type { SelectValue } from 'ant-design-vue/es/select';

  import type { CompanyInfo, ConnectionStatus } from '../types/chat';

  import { Select, Tooltip } from 'ant-design-vue';

  interface Props {
    companyList: CompanyInfo[];
    connectionStatus: ConnectionStatus;
    monthOptions: Array<{ label: string; value: string }>;
    selectedCompany: string;
    selectedMonth: string;
  }

  interface Emits {
    (e: 'company-change', value: string): void;
    (e: 'month-change', value: string): void;
    (e: 'reconnect'): void;
  }

  defineProps<Props>();
  const emit = defineEmits<Emits>();

  const handleCompanyChange = (value: SelectValue) => {
    if (value && typeof value === 'string') {
      emit('company-change', value);
    }
  };

  const handleMonthChange = (value: SelectValue) => {
    if (value && typeof value === 'string') {
      emit('month-change', value);
    }
  };

  const handleReconnect = () => {
    emit('reconnect');
  };
</script>

<template>
  <div class="chat-header">
    <div class="header-content">
      <!-- <div class="header-title">
        <img
          src="https://cdn-icons-png.flaticon.com/128/8943/8943377.png"
          class="title-icon"
        />
        <span class="title-text">精算家AI会计助手</span>
        <div
          class="connection-status"
          :class="connectionStatus.status.toLowerCase()"
        >
          <div class="status-dot"></div>
          <span class="status-text">
            {{
              connectionStatus.isConnected
                ? '已连接'
                : connectionStatus.isConnecting
                  ? '连接中'
                  : '未连接'
            }}
          </span>
          <Button
            v-if="connectionStatus.isClosed"
            type="text"
            size="small"
            @click="handleReconnect"
            class="reconnect-btn"
            title="点击重连"
          >
            🔄
          </Button>
        </div>
      </div> -->

      <!-- 第二行：选择器 -->
      <div class="header-controls">
        <div class="control-group">
          <!-- <label class="control-label">公司:</label> -->
          <Tooltip :title="selectedCompany || '请选择公司'">
            <Select
              :value="selectedCompany"
              style="width: 200px !important"
              placeholder="选择公司"
              size="small"
              @change="handleCompanyChange"
              show-search
              :filter-option="
                (input, option) =>
                  String(option?.children || '')
                    .toLowerCase()
                    .includes(input.toLowerCase())
              "
            >
              <Select.Option
                v-for="company in companyList"
                :key="company.id"
                :value="company.name"
                :title="company.name"
              >
                {{ company.name }}
              </Select.Option>
            </Select>
          </Tooltip>
        </div>

        <div class="control-group">
          <!-- <label class="control-label">月份:</label> -->
          <Select
            :value="selectedMonth"
            placeholder="月份"
            style="width: 120px !important"
            size="small"
            @change="handleMonthChange"
          >
            <Select.Option
              v-for="month in monthOptions"
              :key="month.value"
              :value="month.value"
            >
              {{ month.label }}
            </Select.Option>
          </Select>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
  @keyframes pulse {
    0%,
    100% {
      opacity: 1;
      transform: scale(1);
    }

    50% {
      opacity: 0.7;
      transform: scale(1.2);
    }
  }

  @keyframes pulse {
    0%,
    100% {
      opacity: 1;
      transform: scale(1);
    }

    50% {
      opacity: 0.7;
      transform: scale(1.2);
    }
  }

  .chat-header {
    width: 100% !important;
    padding: 8px 16px !important;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    border-bottom: none !important;
    box-shadow: 0 2px 8px rgb(0 0 0 / 10%) !important;

    .header-content {
      display: flex !important;
      flex-direction: column !important;
      gap: 6px !important;
      align-items: center !important;
      justify-content: center !important;
      width: 100% !important;
    }

    .header-title {
      display: flex !important;
      gap: 10px !important;
      align-items: center !important;
      justify-content: center !important;

      .title-icon {
        width: 24px !important;
        height: 24px !important;
        border-radius: 6px !important;
      }

      .title-text {
        font-size: 15px !important;
        font-weight: 600 !important;
        color: white !important;
        text-shadow: 0 1px 2px rgb(0 0 0 / 10%) !important;
      }

      .connection-status {
        display: flex !important;
        gap: 4px !important;
        align-items: center !important;
        padding: 2px 8px !important;
        background: rgb(255 255 255 / 20%) !important;
        backdrop-filter: blur(10px) !important;
        border-radius: 12px !important;

        .status-dot {
          width: 6px !important;
          height: 6px !important;
          border-radius: 50% !important;
          transition: all 0.3s ease !important;
        }

        .status-text {
          font-size: 10px !important;
          font-weight: 500 !important;
          color: white !important;
          text-shadow: 0 1px 2px rgb(0 0 0 / 10%) !important;
        }

        &.open {
          .status-dot {
            background: #52c41a !important;
            box-shadow: 0 0 8px rgb(82 196 26 / 50%) !important;
          }
        }

        &.connecting {
          .status-dot {
            background: #faad14 !important;
            box-shadow: 0 0 8px rgb(250 173 20 / 50%) !important;
            animation: pulse 1.5s infinite !important;
          }
        }

        &.closed {
          .status-dot {
            background: #ff4d4f !important;
            box-shadow: 0 0 8px rgb(255 77 79 / 50%) !important;
          }
        }

        .reconnect-btn {
          min-width: 16px !important;
          height: 16px !important;
          padding: 0 4px !important;
          font-size: 10px !important;
          color: white !important;
          background: transparent !important;
          border: none !important;
          transition: all 0.2s ease !important;

          &:hover {
            background: rgb(255 255 255 / 20%) !important;
            transform: rotate(180deg) !important;
          }
        }
      }
    }

    .header-controls {
      display: flex !important;
      gap: 16px !important;
      align-items: center !important;
      justify-content: center !important;
    }

    .control-group {
      display: flex !important;
      gap: 4px !important;
      align-items: center !important;

      .control-label {
        font-size: 11px !important;
        font-weight: 500 !important;
        color: white !important;
        text-shadow: 0 1px 2px rgb(0 0 0 / 10%) !important;
        white-space: nowrap !important;
      }
    }

    :deep(.ant-select) {
      margin-right: 0 !important;
      border-radius: 6px !important;
      box-shadow: 0 2px 8px rgb(255 255 255 / 20%) !important;

      .ant-select-selector {
        height: 28px !important;
        font-size: 12px !important;
        line-height: 26px !important;
        background: rgb(255 255 255 / 90%) !important;
        backdrop-filter: blur(10px) !important;
        border: 1px solid rgb(255 255 255 / 30%) !important;
      }

      .ant-select-selection-item {
        font-size: 12px !important;
        line-height: 26px !important;
      }

      .ant-select-selection-placeholder {
        font-size: 12px !important;
        line-height: 26px !important;
      }
    }

    :deep(.ant-picker) {
      width: 120px !important;
      border-radius: 8px !important;
      box-shadow: 0 2px 8px rgb(255 255 255 / 20%) !important;

      .ant-picker-input {
        background: rgb(255 255 255 / 90%) !important;
        backdrop-filter: blur(10px) !important;
        border: 1px solid rgb(255 255 255 / 30%) !important;
      }
    }
  }
</style>
