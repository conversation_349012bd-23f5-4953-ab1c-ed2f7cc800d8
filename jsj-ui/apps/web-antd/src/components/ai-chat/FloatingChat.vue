<script setup lang="ts">
  import { ref } from 'vue';

  import { RobotOutlined } from '@ant-design/icons-vue';

  const isOpen = ref(false);

  const toggleChat = () => {
    isOpen.value = !isOpen.value;
  };
</script>

<template>
  <div
    class="floating-chat"
    :class="{ 'floating-chat-open': isOpen }"
    :style="{ width: isOpen ? '400px' : '48px' }"
  >
    <div class="chat-toggle" @click="toggleChat">
      <RobotOutlined :style="{ fontSize: '24px', color: '#1890ff' }" />
    </div>
    <div class="chat-container" v-show="isOpen">
      <!-- <AiChat /> -->
    </div>
  </div>
</template>

<style scoped lang="scss">
  .floating-chat {
    position: fixed;
    top: 80px;
    bottom: 0;
    left: 0;
    z-index: 1000;
    display: flex;
    flex-direction: column;
    background: #fff;
    border-right: 1px solid #f0f0f0;
    box-shadow: 2px 0 8px rgb(0 0 0 / 15%);
    transition: width 0.3s ease;
  }

  .chat-toggle {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 48px;
    height: 48px;
    cursor: pointer;
    border-bottom: 1px solid #f0f0f0;

    &:hover {
      background: #f5f5f5;
    }
  }

  .chat-container {
    flex: 1;
    overflow: hidden;
  }

  .floating-chat-open {
    .chat-toggle {
      width: 100%;
    }
  }
</style>
