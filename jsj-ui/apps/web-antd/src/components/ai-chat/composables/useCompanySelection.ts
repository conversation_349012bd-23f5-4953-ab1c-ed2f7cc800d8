import { computed, ref } from 'vue';
import { useRouter } from 'vue-router';
import dayjs from 'dayjs';
import { getUserCustomerNames } from '#/api/tool/company';
import { useCompanySelectionStore } from '#/store/modules/company-selection';
import { useMonthSelectionStore } from '#/store/modules/month-selection';
import type { CompanyInfo } from '../types/chat';
import { useUserStore } from '@vben/stores';
import { useLoginTenantId } from '#/views/_core/oauth-common';

export function useCompanySelection() {
  const companySelectionStore = useCompanySelectionStore();
  const monthSelectionStore = useMonthSelectionStore();
  const router = useRouter();
  const userStore = useUserStore();
  const { loginClientId } = useLoginTenantId();

  const companyList = ref<CompanyInfo[]>([]);
  const isInitializing = ref(true);
  const initializationError = ref<string | null>(null);

  // 获取公司列表
  const fetchCompanyNames = async () => {
    try {
      isInitializing.value = true;
      initializationError.value = null;
      const username = userStore.userInfo?.username || '';
      const tenant_id = userStore.userInfo?.tenantId || '';
      if (!username || !tenant_id) {
        initializationError.value = '缺少用户信息或租户ID';
        isInitializing.value = false;
        return;
      }
      const response = await getUserCustomerNames({ username, tenant_id });
      console.log('response44', response);
      const customer_names=response?.data?.data?.customer_names
      if (
        customer_names &&
        Array.isArray(customer_names)
      ) {
        companyList.value = customer_names.map((name: string) => ({
          id: name,
          name,
        }));
        if (companyList.value.length > 0) {
          const currentSelected = companySelectionStore.getSelectedCompany();
          const isCurrentSelectedValid = companyList.value.some(
            (company) => company.name === currentSelected,
          );
          if (isCurrentSelectedValid) {
            // 保持当前选中的公司
          } else if (companyList.value[0]) {
            companySelectionStore.setSelectedCompany(companyList.value[0].name);
          }
        }
        isInitializing.value = false;
      } else {
        initializationError.value = '客户列表数据格式不正确';
        isInitializing.value = false;
      }
    } catch (error) {
      initializationError.value = '获取客户列表失败，请检查网络连接';
      isInitializing.value = false;
      companyList.value = [{ id: 'default', name: '默认公司' }];
      companySelectionStore.setSelectedCompany('默认公司');
    }
  };

  // 检查是否在AI凭证页面并重新加载数据
  const checkAndReloadVoucherData = () => {
    const currentRoute = router.currentRoute.value;
    if (currentRoute.path === '/accountingVouchers/voucher-overview2') {
      window.dispatchEvent(new CustomEvent('reload-voucher-data'));
    }
  };

  // 处理公司选择变化
  const handleCompanyChange = (value: any, onUpdate: () => void) => {
    if (typeof value === 'string') {
      companySelectionStore.setSelectedCompany(value);
      onUpdate();
      checkAndReloadVoucherData();
    }
  };

  // 处理月份选择变化
  const handleMonthChange = (date: any, onUpdate: () => void) => {
    if (typeof date === 'string') {
      monthSelectionStore.setSelectedMonth(dayjs(date));
    } else if (date === null) {
      monthSelectionStore.setSelectedMonth(dayjs());
    } else if (date) {
      monthSelectionStore.setSelectedMonth(date);
    }
    onUpdate();
    checkAndReloadVoucherData();
  };

  // 处理月份选择变化（Select组件）
  const handleMonthSelectChange = (value: string, onUpdate: () => void) => {
    monthSelectionStore.setSelectedMonth(dayjs(value, 'YYYYMM'));
    onUpdate();
    checkAndReloadVoucherData();
  };

  // 生成月份选项
  const monthOptions = computed(() => {
    const options = [];
    const currentDate = dayjs();

    for (let i = 0; i < 12; i++) {
      const date = currentDate.subtract(i, 'month');
      options.push({
        label: date.format('YYYY年MM月'),
        value: date.format('YYYYMM'),
      });
    }

    return options;
  });

  // 当前选中的月份
  const selectedMonth = computed({
    get: () => monthSelectionStore.getFormattedMonth(),
    set: (value: string) => {
      monthSelectionStore.setSelectedMonth(dayjs(value, 'YYYYMM'));
    },
  });

  const selectedCompany = computed({
    get: () => companySelectionStore.getSelectedCompany(),
    set: (value: string) => {
      companySelectionStore.setSelectedCompany(value);
    },
  });

  return {
    companyList,
    isInitializing,
    initializationError,
    selectedCompany,
    selectedMonth,
    monthOptions,
    fetchCompanyNames,
    handleCompanyChange,
    handleMonthChange,
    handleMonthSelectChange,
    checkAndReloadVoucherData,
  };
}
