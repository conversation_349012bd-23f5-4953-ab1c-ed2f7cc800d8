import { ref } from 'vue';
import { message } from 'ant-design-vue';
import type { UploadFile, UploadFileStatus } from 'ant-design-vue/es/upload/interface';
import { uploadMultipleFiles } from '#/api/tool/upload';
import type { UploadedFile } from '../types/chat';

export function useFileUpload() {
  const uploadedFiles = ref<UploadedFile[]>([]);
  const items = ref<UploadFile[]>([]);
  const isOpen = ref(false);

  const fileChange = async ({ fileList }: { fileList: UploadFile[] }, companyName: string) => {
    try {
      console.log(
        'fileChange called with fileList:',
        fileList.map((f) => ({ name: f.name, status: f.status, uid: f.uid })),
      );

      // 过滤出新上传的文件（状态不是done且有原始文件对象）
      const newFiles = fileList
        .filter((file) => {
          const isNewFile = file.status !== 'done' && file.originFileObj;
          console.log(
            `File ${file.name}: status=${file.status}, hasOriginFileObj=${!!file.originFileObj}, isNewFile=${isNewFile}`,
          );
          return isNewFile;
        })
        .map((file) => file.originFileObj as File);

      console.log(
        'New files to upload:',
        newFiles.map((f) => f.name),
      );

      if (newFiles.length > 0) {
        // 显示上传中状态
        const loadingMessage = message.loading(
          `正在上传 ${newFiles.length} 个文件...`,
          0,
        );

        try {
          // 上传文件到服务器，传递当前选中的公司名称
          const responses: any = await uploadMultipleFiles(newFiles, {
            company_name: companyName,
          });
          console.log('Upload responses:', responses);

          // 将所有响应的文件追加到 uploadedFiles 中
          const newUploadedFiles = responses.flatMap(
            (response: any) => response.data.files,
          );
          console.log('New uploaded files:', newUploadedFiles);
          uploadedFiles.value.push(...newUploadedFiles);

          // 更新文件列表中的 URL
          const updatedFileList = fileList.map((file, index) => {
            if (index >= fileList.length - newFiles.length) {
              const fileIndex = index - (fileList.length - newFiles.length);
              const fileInfo = newUploadedFiles[fileIndex];
              console.log('fileInfo', fileInfo);
              if (fileInfo) {
                return {
                  ...file,
                  name: fileInfo.original_filename,
                  status: 'done' as UploadFileStatus,
                  url: fileInfo.file_path,
                };
              }
            }
            return file;
          });

          // 更新文件列表
          items.value = updatedFileList;

          // 关闭加载提示并显示成功消息
          message.destroy();
          message.success(`成功上传 ${newFiles.length} 个文件`);
        } catch (uploadError) {
          console.error('Upload error:', uploadError);
          message.destroy();
          message.error('文件上传失败，请重试');
        }
      } else {
        // 处理文件删除的情况
        const currentFileUrls = new Set(
          fileList
            .filter((file) => file.status === 'done' && file.url)
            .map((file) => file.url),
        );

        // 根据当前文件列表同步更新 uploadedFiles
        uploadedFiles.value = uploadedFiles.value.filter((uploadedFile) => {
          return currentFileUrls.has(uploadedFile.file_path);
        });

        // 更新文件列表
        items.value = fileList;

        console.log(
          '文件列表已更新，当前上传文件数量:',
          uploadedFiles.value.length,
        );
      }
    } catch (error) {
      console.error('File upload failed:', error);
      message.destroy();
      message.error('文件上传失败');
    }
  };

  const handleFileUpload = (firstFile: File, fileList: FileList, attachmentsRef: any) => {
    console.log(
      'handleFileUpload called with files:',
      [...fileList].map((f) => f.name),
    );

    try {
      // 确保附件面板是打开的
      isOpen.value = true;

      // 逐个上传文件，添加延迟以避免并发问题
      [...fileList].forEach((file, index) => {
        setTimeout(() => {
          console.log(
            `Uploading file ${index + 1}/${fileList.length}:`,
            file.name,
          );
          if (attachmentsRef.value) {
            attachmentsRef.value.upload(file);
          } else {
            console.error(
              'attachmentsRef is null when trying to upload file:',
              file.name,
            );
          }
        }, index * 100); // 每个文件间隔100ms上传
      });
    } catch (error) {
      console.error('Error in handleFileUpload:', error);
      message.error('文件上传处理失败');
    }
  };

  const clearFiles = () => {
    uploadedFiles.value = [];
    items.value = [];
    isOpen.value = false;
  };

  const getFilesForMessage = () => {
    return uploadedFiles.value
      .filter((file) => file && file.saved_filename)
      .map((file) => ({
        location_type: 'uploaded_file',
        url: file.saved_filename,
      }));
  };

  return {
    uploadedFiles,
    items,
    isOpen,
    fileChange,
    handleFileUpload,
    clearFiles,
    getFilesForMessage,
  };
}
