import { computed, h } from 'vue';
import { useRouter } from 'vue-router';
import { Button, message } from 'ant-design-vue';
import { MoreOutlined } from '@ant-design/icons-vue';
import { useAIChatStore } from '#/store/modules/ai-chat';
import type { ChatMessage } from '#/store/modules/ai-chat';
import type { WebSocketMessage } from '../types/chat';

export function useMessageHandling(taskProgressMap: Map<string, any>) {
  const aiChatStore = useAIChatStore();
  const router = useRouter();

  // 用于跟踪已处理的跳转，防止重复跳转
  const processedNavigations = new Set<string>();

  const loading = computed(() => aiChatStore.isProcessing);
  const messages = computed(() => {
    const msgs = aiChatStore.currentMessages;
    console.log('当前消息列表:', msgs);
    return msgs;
  });

  // 处理WebSocket消息
  const handleWebSocketMessage = (response: WebSocketMessage, scrollToBottom: () => void) => {
    try {
      if (!response.message) {
        return;
      }

      let content = '';
      let extraData: ChatMessage['extraData'];

      switch (response.type) {
        case 'bank_receipt_extracted': {
          const taskId = response.task_id;
          content = response.message || '银行回单数据提取完成';
          if (response.data) {
            const bankReceiptData = {
              account_name: response.data.account_name || '',
              account_number: response.data.account_number || '',
              amount: response.data.amount || 0,
              bank_name: response.data.bank_name || '',
              currency: response.data.currency || 'CNY',
              note: response.data.note,
              summary: response.data.summary || '',
              transaction_time: response.data.transaction_time || '',
              type: response.data.type || '',
            };

            const existingMessage = aiChatStore.findMessageByTaskId(
              taskId!,
              'bank_receipt_extracted',
            );
            if (existingMessage) {
              aiChatStore.updateMessageByTaskId(
                taskId!,
                'bank_receipt_extracted',
                {
                  content,
                  extraData: {
                    data: bankReceiptData,
                    type: 'bank_receipt_extracted',
                  },
                  status: 'success',
                  type: 'bank_receipt_extracted',
                },
              );
              return;
            }

            extraData = {
              data: bankReceiptData,
              type: 'bank_receipt_extracted',
            };
          }
          break;
        }
        case 'client_info_updated': {
          const companyInfo = response.company_name
            ? `公司：${response.company_name}`
            : '';
          const monthInfo = response.month ? `月份：${response.month}` : '';
          const infoText = [companyInfo, monthInfo]
            .filter(Boolean)
            .join('，');
          if (infoText) {
            message.info(`已更新 ${infoText}`);
          }
          return;
        }
        case 'company_name_updated': {
          message.info(
            `已切换到公司：${response.company_name || '未知公司'}`,
          );
          return;
        }
        case 'salary_data_extracted': {
          const taskId = response.task_id;
          content = response.message || '工资单数据提取完成';
          if (response.data) {
            const salaryData = {
              company_name: response.company_name || '',
              employee_count: response.data.employee_count || 0,
              employees: response.data.employees || [],
              total_actual_salary: response.data.total_actual_salary || 0,
              total_salary: response.data.total_salary || 0,
            };

            const existingMessage = aiChatStore.findMessageByTaskId(
              taskId!,
              'salary_data_extracted',
            );
            if (existingMessage) {
              aiChatStore.updateMessageByTaskId(
                taskId!,
                'salary_data_extracted',
                {
                  content,
                  extraData: {
                    data: salaryData,
                    type: 'salary_data_extracted',
                  },
                  status: 'success',
                  type: 'salary_data_extracted',
                },
              );
              return;
            }

            extraData = {
              data: salaryData,
              type: 'salary_data_extracted',
            };
          }
          break;
        }
        case 'task_status': {
          const taskId = response.task_id;
          const messageText = response.message;

          // 检查是否需要跳转到凭证页面
          if (messageText === '银行回单处理已完成' && taskId && !processedNavigations.has(taskId)) {
            processedNavigations.add(taskId);
            setTimeout(() => {
              const currentRoute = router.currentRoute.value;
              if (currentRoute.path === '/accountingVouchers/voucher-overview2') {
                window.dispatchEvent(new CustomEvent('reload-voucher-data'));
                message.success('银行回单处理已完成，数据已刷新');
              } else {
                router.push('/accountingVouchers/voucher-overview2');
              }
            }, 1000);
          }

          // 检查发票处理完成是否需要跳转到凭证页面
          if (messageText === '发票处理已完成' && taskId && !processedNavigations.has(taskId)) {
            processedNavigations.add(taskId);
            setTimeout(() => {
              const currentRoute = router.currentRoute.value;
              if (currentRoute.path === '/accountingVouchers/voucher-overview2') {
                window.dispatchEvent(new CustomEvent('reload-voucher-data'));
                message.success('发票处理已完成，数据已刷新');
              } else {
                router.push('/accountingVouchers/voucher-overview2');
              }
            }, 1000);
          }

          if (response.progress !== undefined) {
            const newThoughtChainItem = {
              data: {
                data: response.data || {},
                message: response.message,
                progress: response.progress,
                result: response.result || {},
                sender: response.sender,
                status: response.status,
                task_id: response.task_id,
                type: response.type,
              },
              description: response.message || '',
              extra: h(Button, { icon: h(MoreOutlined), type: 'text' }),
              status: response.progress === 100 ? 'success' : 'pending',
              timestamp: new Date(),
              title: response.status || '处理中',
            };

            taskProgressMap.set(taskId!, {
              message: messageText,
              progress: response.progress,
            });

            const messageType = 'task_status';
            const existingMessage = aiChatStore.findMessageByTaskId(
              taskId!,
              messageType,
            );
            
            if (existingMessage) {
              const updatedThoughtChainItems =
                existingMessage.thoughtChainItems || [];
              updatedThoughtChainItems.push(newThoughtChainItem);

              updatedThoughtChainItems.sort((a, b) => {
                const timeA = a.timestamp ? a.timestamp.getTime() : 0;
                const timeB = b.timestamp ? b.timestamp.getTime() : 0;
                return timeA - timeB;
              });

              aiChatStore.updateMessageByTaskId(taskId!, messageType, {
                content: messageText!,
                status: 'success',
                thoughtChainItems: updatedThoughtChainItems,
                type: messageType,
              });

              // 滚动到底部
              scrollToBottom();
              return;
            }

            aiChatStore.addMessage({
              content: messageText!,
              status: 'success',
              taskId: taskId!,
              thoughtChainItems: [newThoughtChainItem],
              type: messageType,
            });

            // 滚动到底部
            scrollToBottom();
            return;
          }
          break;
        }
        case 'voucher_generation_completed': {
          content = response.message || '凭证生成完成';
          extraData = {
            data: response.data,
            type: 'voucher_generation_completed',
          };

          setTimeout(() => {
            const currentRoute = router.currentRoute.value;
            if (
              currentRoute.path === '/accountingVouchers/voucher-overview2'
            ) {
              window.dispatchEvent(new CustomEvent('reload-voucher-data'));
              message.success('凭证生成完成，数据已刷新');
            } else {
              router.push('/accountingVouchers/voucher-overview2');
            }
          }, 1000);
          break;
        }
        default: {
          content = response.message || '收到消息';
        }
      }

      if (
        response.type === 'task_status' &&
        response.progress !== undefined
      ) {
        // 已经在上面处理过
      } else {
        aiChatStore.addMessage({
          content,
          extraData,
          status: 'success',
          taskId: response.task_id,
          type: (response.type as ChatMessage['type']) || 'ai',
          ...(response.type === 'bank_receipt_extracted'
            ? {
                messages: [
                  {
                    content,
                    timestamp: new Date(),
                    type: extraData?.type || 'message',
                  },
                ],
              }
            : {}),
          ...(response.type === 'salary_data_extracted'
            ? {
                messages: [
                  {
                    content,
                    timestamp: new Date(),
                    type: extraData?.type || 'message',
                  },
                ],
              }
            : {}),
          ...(response.type === 'voucher_generation_completed'
            ? {
                messages: [
                  {
                    content,
                    timestamp: new Date(),
                    type: 'message',
                  },
                ],
              }
            : {}),
        });

        // 滚动到底部
        scrollToBottom();
      }
    } catch (error) {
      console.error('Error parsing WebSocket message:', error);
    }
  };

  // 处理消息提交
  const handleSubmit = async (
    content: string,
    uploadedFiles: any[],
    sendMessageWithFiles: (content: string) => void,
    scrollToBottom: () => void,
  ) => {
    const hasContent = content.trim().length > 0;
    const hasFiles = uploadedFiles.length > 0;

    if (!hasContent && !hasFiles) {
      message.warning('请输入内容或上传文件');
      return;
    }

    if (loading.value) return;

    let messageContent = content.trim();
    if (hasFiles) {
      const fileNames = uploadedFiles
        .map((file) => file.original_filename)
        .join(', ');
      if (hasContent) {
        messageContent += `\n\n📎 附件: ${fileNames}`;
      } else {
        messageContent = `📎 发送了 ${uploadedFiles.length} 个文件: ${fileNames}`;
      }
    }

    if (hasContent || hasFiles) {
      aiChatStore.addMessage({
        content: messageContent,
        status: 'success',
        type: 'user',
      });
    }

    // 滚动到底部
    scrollToBottom();

    try {
      sendMessageWithFiles(content);
    } catch (error) {
      console.error('Error sending message:', error);
      message.error('发送消息失败，请重试');
    }
  };

  return {
    loading,
    messages,
    handleWebSocketMessage,
    handleSubmit,
  };
}
