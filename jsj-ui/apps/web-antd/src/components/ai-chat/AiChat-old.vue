<script setup lang="ts">
  import type {
    UploadFile,
    UploadFileStatus,
  } from 'ant-design-vue/es/upload/interface';
  import type {
    BubbleListProps,
    PromptsProps,
    SuggestionProps,
  } from 'ant-design-x-vue';
  import type { Dayjs } from 'dayjs';

  import type { ChatMessage } from '#/store/modules/ai-chat';

  import {
    computed,
    h,
    nextTick,
    onMounted,
    onUnmounted,
    ref,
    watch,
  } from 'vue';
  import { useRouter } from 'vue-router';

  import {
    CloudUploadOutlined,
    LinkOutlined,
    MoreOutlined,
    RobotOutlined,
    SmileOutlined,
    UserOutlined,
  } from '@ant-design/icons-vue';
  import { useWebSocket } from '@vueuse/core';
  import { Button, Flex, message, Select, Tooltip } from 'ant-design-vue';
  import {
    Attachments,
    Bubble,
    Prompts,
    Sender,
    Suggestion,
    Welcome,
  } from 'ant-design-x-vue';
  import dayjs from 'dayjs';

  import { getCompanyNames } from '#/api/tool/company';
  import { uploadMultipleFiles } from '#/api/tool/upload';
  import { useAIChatStore } from '#/store/modules/ai-chat';
  import { useCompanySelectionStore } from '#/store/modules/company-selection';
  import { useMonthSelectionStore } from '#/store/modules/month-selection';

  import MessageDetailModal from './MessageDetailModal.vue';

  import 'dayjs/locale/zh-cn';

  // 设置dayjs的默认locale
  try {
    dayjs.locale('zh-cn');
  } catch (error) {
    console.warn('Failed to set dayjs locale to zh-cn:', error);
    // 如果设置中文locale失败，使用默认的英文locale
  }

  const aiChatStore = useAIChatStore();
  const companySelectionStore = useCompanySelectionStore();
  const monthSelectionStore = useMonthSelectionStore();
  const router = useRouter();

  const WS_URL = 'ws://**************:30065';

  // 公司列表
  const companyList = ref<Array<{ id: string; name: string }>>([]);

  // 初始化状态
  const isInitializing = ref(true);
  const initializationError = ref<null | string>(null);

  // 获取公司列表
  const fetchCompanyNames = async () => {
    try {
      console.log('开始获取公司列表...');
      const response = await getCompanyNames();
      console.log('获取公司列表成功:', response);

      // 修改这里，正确访问嵌套的公司名称数组
      if (
        response?.data?.data?.company_names &&
        Array.isArray(response.data.data.company_names)
      ) {
        companyList.value = response.data.data.company_names.map(
          (name: string) => ({
            id: name, // 使用公司名作为id
            name,
          }),
        );

        console.log('公司列表处理完成:', companyList.value);

        if (companyList.value.length > 0) {
          // 检查当前选中的公司是否在列表中
          const currentSelected = companySelectionStore.getSelectedCompany();
          const isCurrentSelectedValid = companyList.value.some(
            (company) => company.name === currentSelected,
          );

          if (isCurrentSelectedValid) {
            console.log('保持当前选中的公司:', currentSelected);
          } else {
            // 如果当前选中的公司不在列表中，选择第一个
            companySelectionStore.setSelectedCompany(companyList.value[0].name);
            console.log('默认选择公司:', companyList.value[0].name);
          }
        }
      } else {
        console.warn('公司列表数据格式不正确:', response);
        message.warning('公司列表数据格式不正确');
      }
    } catch (error) {
      console.error('获取公司列表失败:', error);
      message.error('获取公司列表失败，请检查网络连接');
      // 设置一个默认的公司列表以防止页面完全空白
      companyList.value = [{ id: 'default', name: '默认公司' }];
      companySelectionStore.setSelectedCompany('默认公司');
    }
  };

  // 发送更新客户端信息的消息（包含公司名称和月份）
  const sendUpdateClientInfoMessage = () => {
    const selectedCompany = companySelectionStore.getSelectedCompany();
    const selectedMonth = monthSelectionStore.getFormattedMonth();
    if (!selectedCompany) return;

    const updateClientInfoMessage = {
      company_name: selectedCompany,
      month: selectedMonth,
      type: 'update_client_info',
    };

    const jsonStr = JSON.stringify(updateClientInfoMessage);
    console.log('发送更新客户端信息消息:', jsonStr);

    wsSend(jsonStr);
  };

  // 发送更新公司名称的消息（保持向后兼容）
  const sendUpdateCompanyMessage = () => {
    sendUpdateClientInfoMessage();
  };

  // 检查是否在AI凭证页面并重新加载数据
  const checkAndReloadVoucherData = () => {
    const currentRoute = router.currentRoute.value;
    if (currentRoute.path === '/accountingVouchers/voucher-overview2') {
      // 通过事件总线通知凭证页面重新加载数据
      window.dispatchEvent(new CustomEvent('reload-voucher-data'));
    }
  };

  // 处理公司选择变化
  const handleCompanyChange = (value: any) => {
    if (typeof value === 'string') {
      companySelectionStore.setSelectedCompany(value);
      sendUpdateCompanyMessage();
      // 检查是否需要重新加载凭证数据
      checkAndReloadVoucherData();
    }
  };

  // 处理月份选择变化
  const handleMonthChange = (date: Dayjs | null | string) => {
    if (typeof date === 'string') {
      monthSelectionStore.setSelectedMonth(dayjs(date));
    } else if (date === null) {
      monthSelectionStore.setSelectedMonth(dayjs());
    } else if (date) {
      monthSelectionStore.setSelectedMonth(date);
    }
    // 发送更新客户端信息的消息
    sendUpdateClientInfoMessage();
    // 检查是否需要重新加载凭证数据
    checkAndReloadVoucherData();
  };

  // 处理月份选择变化（Select组件）
  const handleMonthSelectChange = (value: string) => {
    monthSelectionStore.setSelectedMonth(dayjs(value, 'YYYYMM'));
    // 发送更新客户端信息的消息
    sendUpdateClientInfoMessage();
    // 检查是否需要重新加载凭证数据
    checkAndReloadVoucherData();
  };

  // 生成月份选项
  const monthOptions = computed(() => {
    const options = [];
    const currentDate = dayjs();

    // 生成过去12个月的选项
    for (let i = 0; i < 12; i++) {
      const date = currentDate.subtract(i, 'month');
      options.push({
        label: date.format('YYYY年MM月'),
        value: date.format('YYYYMM'),
      });
    }

    return options;
  });

  // 当前选中的月份
  const selectedMonth = computed({
    get: () => monthSelectionStore.getFormattedMonth(),
    set: (value: string) => {
      monthSelectionStore.setSelectedMonth(dayjs(value, 'YYYYMM'));
      // 发送更新客户端信息的消息
      sendUpdateClientInfoMessage();
      // 检查是否需要重新加载凭证数据
      checkAndReloadVoucherData();
    },
  });

  // 用于存储任务进度的 Map
  const taskProgressMap = new Map();

  const {
    close: wsClose,
    open: wsOpen,
    send: wsSend,
    status: wsStatus,
  } = useWebSocket(WS_URL, {
    autoReconnect: {
      delay: 3000, // 重连延迟 3 秒
      onFailed() {
        console.error('WebSocket重连失败，已达到最大重试次数');
        message.error('网络连接异常，请刷新页面重试');
      },
      retries: 5, // 最大重试次数
    },
    heartbeat: {
      interval: 30_000, // 每30秒发送一次心跳
      message: JSON.stringify({ timestamp: Date.now(), type: 'ping' }), // 心跳消息
      pongTimeout: 10_000, // 等待响应超时时间10秒
    },
    immediate: false, // 禁用自动连接，需要手动调用 wsOpen()
    onConnected: () => {
      console.log('WebSocket连接成功');
      message.success('连接成功');
      // WebSocket连接成功后发送更新公司名称的消息
      sendUpdateCompanyMessage();
    },
    onDisconnected: (ws, event) => {
      console.log('WebSocket连接断开', event);
      // 只有在非正常关闭时才显示断开提示
      if (event.code !== 1000) {
        message.warning('连接已断开，正在尝试重连...');
      }
    },
    onError: (ws, event) => {
      console.error('WebSocket连接错误:', event);
      message.error('WebSocket连接失败，正在尝试重连...');
    },
    onMessage: (_ws: WebSocket, event: MessageEvent) => {
      try {
        const response = JSON.parse(event.data);
        // 处理心跳响应
        // if (response.type === 'pong') {
        //   console.log('收到心跳响应');
        //   return;
        // }

        if (!response.message) {
          return;
        }

        let content = '';
        let extraData: ChatMessage['extraData'];

        switch (response.type) {
          case 'bank_receipt_extracted': {
            // 银行回单
            const taskId = response.task_id;
            content = response.message || '银行回单数据提取完成';
            if (response.data) {
              const bankReceiptData = {
                account_name: response.data.account_name || '',
                account_number: response.data.account_number || '',
                amount: response.data.amount || 0,
                bank_name: response.data.bank_name || '',
                currency: response.data.currency || 'CNY',
                note: response.data.note,
                summary: response.data.summary || '',
                transaction_time: response.data.transaction_time || '',
                type: response.data.type || '',
              };

              // 查找并更新现有消息
              const existingMessage = aiChatStore.findMessageByTaskId(
                taskId,
                'bank_receipt_extracted',
              );
              console.log('existingMessage', existingMessage);
              if (existingMessage) {
                aiChatStore.updateMessageByTaskId(
                  taskId,
                  'bank_receipt_extracted',
                  {
                    content,
                    extraData: {
                      data: bankReceiptData,
                      type: 'bank_receipt_extracted',
                    },
                    status: 'success',
                    type: 'bank_receipt_extracted',
                  },
                );
                return;
              }

              // 如果没有现有消息，设置extraData以便在添加新消息时使用
              extraData = {
                data: bankReceiptData,
                type: 'bank_receipt_extracted',
              };
            }
            break;
          }
          case 'client_info_updated': {
            // 客户端信息更新消息（包含公司和月份），不显示在聊天界面，使用全局通知
            const companyInfo = response.company_name
              ? `公司：${response.company_name}`
              : '';
            const monthInfo = response.month ? `月份：${response.month}` : '';
            const infoText = [companyInfo, monthInfo]
              .filter(Boolean)
              .join('，');
            if (infoText) {
              message.info(`已更新 ${infoText}`);
            }
            return; // 直接返回，不添加到聊天记录
          }
          case 'company_name_updated': {
            // 公司名称更新消息，不显示在聊天界面，使用全局通知
            message.info(
              `已切换到公司：${response.company_name || '未知公司'}`,
            );
            return; // 直接返回，不添加到聊天记录
          }
          case 'salary_data_extracted': {
            // 工资单数据提取
            const taskId = response.task_id;
            content = response.message || '工资单数据提取完成';
            if (response.data) {
              const salaryData = {
                company_name: response.company_name || '',
                employee_count: response.data.employee_count || 0,
                employees: response.data.employees || [],
                total_actual_salary: response.data.total_actual_salary || 0,
                total_salary: response.data.total_salary || 0,
              };

              // 查找并更新现有消息
              const existingMessage = aiChatStore.findMessageByTaskId(
                taskId,
                'salary_data_extracted',
              );
              if (existingMessage) {
                aiChatStore.updateMessageByTaskId(
                  taskId,
                  'salary_data_extracted',
                  {
                    content,
                    extraData: {
                      data: salaryData,
                      type: 'salary_data_extracted',
                    },
                    status: 'success',
                    type: 'salary_data_extracted',
                  },
                );
                return;
              }

              // 如果没有现有消息，设置extraData以便在添加新消息时使用
              extraData = {
                data: salaryData,
                type: 'salary_data_extracted',
              };
            }
            break;
          }
          case 'task_status': {
            const taskId = response.task_id;
            const messageText = response.message;
            console.log('response444', response);
            // 更新任务进度
            if (response.progress !== undefined) {
              // 创建新的thoughtChainItem
              const newThoughtChainItem = {
                // 保存原始数据以便需要时使用
                data: {
                  data: response.data || {},
                  message: response.message,
                  progress: response.progress,
                  result: response.result || {},
                  sender: response.sender,
                  status: response.status,
                  task_id: response.task_id,
                  type: response.type,
                },
                description: response.message || '',
                extra: h(Button, { icon: h(MoreOutlined), type: 'text' }),
                status: response.progress === 100 ? 'success' : 'pending',
                // 保存时间戳，用于排序
                timestamp: new Date(),
                title: response.status || '处理中',
              } satisfies {
                data?: any;
                description?: string;
                extra?: any;
                status?: 'error' | 'pending' | 'success';
                timestamp: Date;
                title: string;
              };

              // 存储或更新任务进度
              taskProgressMap.set(taskId, {
                message: messageText,
                progress: response.progress,
              });

              // 将消息类型设置为task_status
              const messageType = 'task_status';

              // 查找并更新现有消息
              const existingMessage = aiChatStore.findMessageByTaskId(
                taskId,
                messageType,
              );
              if (existingMessage) {
                // 如果已有消息，将新的thoughtChainItem添加到现有的thoughtChainItems数组中
                const updatedThoughtChainItems =
                  existingMessage.thoughtChainItems || [];
                updatedThoughtChainItems.push(newThoughtChainItem);

                // 按时间戳排序，确保按时间顺序显示
                updatedThoughtChainItems.sort((a, b) => {
                  const timeA = a.timestamp ? a.timestamp.getTime() : 0;
                  const timeB = b.timestamp ? b.timestamp.getTime() : 0;
                  return timeA - timeB;
                });

                aiChatStore.updateMessageByTaskId(taskId, messageType, {
                  content: messageText,
                  status: 'success',
                  thoughtChainItems: updatedThoughtChainItems,
                  type: messageType,
                });

                // 滚动到底部
                setTimeout(() => scrollToBottom(), 100);
                return;
              }

              // 如果没有现有消息，直接添加新消息
              aiChatStore.addMessage({
                content: messageText,
                status: 'success',
                taskId,
                thoughtChainItems: [newThoughtChainItem],
                type: messageType,
              });

              // 滚动到底部
              setTimeout(() => scrollToBottom(), 100);
              return; // 已经添加了消息，直接返回
            }
            break;
          }
          case 'voucher_generation_completed': {
            content = response.message || '凭证生成完成';
            extraData = {
              data: response.data,
              type: 'voucher_generation_completed',
            };

            // 收到凭证生成完成消息后的处理
            setTimeout(() => {
              const currentRoute = router.currentRoute.value;
              if (
                currentRoute.path === '/accountingVouchers/voucher-overview2'
              ) {
                // 如果已经在凭证页面，触发数据刷新
                window.dispatchEvent(new CustomEvent('reload-voucher-data'));
                message.success('凭证生成完成，数据已刷新');
              } else {
                // 如果不在凭证页面，跳转到凭证页面
                router.push('/accountingVouchers/voucher-overview2');
              }
            }, 1000); // 延迟1秒后处理，确保消息已显示
            break;
          }
          default: {
            content = response.message || '收到消息';
          }
        }
        console.log('content22', content);
        // 如果是task_status类型的消息，并且已经在上面的代码中处理过，则不需要再次添加
        if (
          response.type === 'task_status' &&
          response.progress !== undefined
        ) {
          // 已经在上面的代码中处理过，不需要再次添加消息
          // 什么都不做
        } else {
          // 添加普通消息
          aiChatStore.addMessage({
            content,
            extraData,
            status: 'success',
            taskId: response.task_id,
            type: response.type || 'ai',
            ...(response.type === 'bank_receipt_extracted'
              ? {
                  messages: [
                    {
                      content,
                      timestamp: new Date(),
                      type: extraData?.type || 'message',
                    },
                  ],
                }
              : {}),
            ...(response.type === 'salary_data_extracted'
              ? {
                  messages: [
                    {
                      content,
                      timestamp: new Date(),
                      type: extraData?.type || 'message',
                    },
                  ],
                }
              : {}),
            ...(response.type === 'voucher_generation_completed'
              ? {
                  messages: [
                    {
                      content,
                      timestamp: new Date(),
                      type: 'message',
                    },
                  ],
                }
              : {}),
          });

          // 滚动到底部
          setTimeout(() => scrollToBottom(), 100);
        }
      } catch (error) {
        console.error('Error parsing WebSocket message:', error);
      }
    },
  });

  // 存储上传的文件信息
  const uploadedFiles = ref<
    Array<{
      file_path: string;
      file_size: number;
      original_filename: string;
      saved_filename: string;
    }>
  >([]);

  const fileChange = async ({ fileList }: { fileList: UploadFile[] }) => {
    try {
      console.log(
        'fileChange called with fileList:',
        fileList.map((f) => ({ name: f.name, status: f.status, uid: f.uid })),
      );

      // 过滤出新上传的文件（状态不是done且有原始文件对象）
      const newFiles = fileList
        .filter((file) => {
          const isNewFile = file.status !== 'done' && file.originFileObj;
          console.log(
            `File ${file.name}: status=${file.status}, hasOriginFileObj=${!!file.originFileObj}, isNewFile=${isNewFile}`,
          );
          return isNewFile;
        })
        .map((file) => file.originFileObj as File);

      console.log(
        'New files to upload:',
        newFiles.map((f) => f.name),
      );

      if (newFiles.length > 0) {
        // 显示上传中状态
        const loadingMessage = message.loading(
          `正在上传 ${newFiles.length} 个文件...`,
          0,
        );

        try {
          // 上传文件到服务器，传递当前选中的公司名称
          const responses: any = await uploadMultipleFiles(newFiles, {
            company_name: companySelectionStore.getSelectedCompany(),
          });
          console.log('Upload responses:', responses);

          // 将所有响应的文件追加到 uploadedFiles 中
          // 从每个响应中提取文件数据
          const newUploadedFiles = responses.flatMap(
            (response: any) => response.data.files,
          );
          console.log('New uploaded files:', newUploadedFiles);
          uploadedFiles.value.push(...newUploadedFiles);

          // 更新文件列表中的 URL
          const updatedFileList = fileList.map((file, index) => {
            if (index >= fileList.length - newFiles.length) {
              const fileIndex = index - (fileList.length - newFiles.length);
              const fileInfo = newUploadedFiles[fileIndex];
              console.log('fileInfo', fileInfo);
              // 直接使用文件信息
              if (fileInfo) {
                return {
                  ...file,
                  name: fileInfo.original_filename,
                  status: 'done' as UploadFileStatus,
                  url: fileInfo.file_path,
                };
              }
            }
            return file;
          });

          // 更新文件列表
          items.value = updatedFileList;

          // 关闭加载提示并显示成功消息
          message.destroy();
          message.success(`成功上传 ${newFiles.length} 个文件`);
        } catch (uploadError) {
          console.error('Upload error:', uploadError);
          message.destroy();
          message.error('文件上传失败，请重试');
        }
      } else {
        // 处理文件删除的情况
        // 当没有新文件上传时，检查是否有文件被删除
        const currentFileUrls = new Set(
          fileList
            .filter((file) => file.status === 'done' && file.url)
            .map((file) => file.url),
        );

        // 根据当前文件列表同步更新 uploadedFiles
        uploadedFiles.value = uploadedFiles.value.filter((uploadedFile) => {
          // 保留那些在当前文件列表中仍然存在的文件
          return currentFileUrls.has(uploadedFile.file_path);
        });

        // 更新文件列表
        items.value = fileList;

        console.log(
          '文件列表已更新，当前上传文件数量:',
          uploadedFiles.value.length,
        );
      }
    } catch (error) {
      console.error('File upload failed:', error);
      message.destroy();
      message.error('文件上传失败');
    }
  };

  // 发送消息（包含文件）
  const sendMessageWithFiles = (content: string = '') => {
    console.log('uploadedFiles.value:', uploadedFiles.value);

    // 处理文件数据，现在 uploadedFiles.value 直接是文件对象数组
    const files = uploadedFiles.value
      .filter((file) => file && file.saved_filename)
      .map((file) => ({
        location_type: 'uploaded_file',
        url: file.saved_filename,
      }));

    const messageData = {
      client_type: 'ai',
      data: {
        company_name: companySelectionStore.getSelectedCompany(),
        files,
        month: monthSelectionStore.getFormattedMonth(),
      },
      task_type: 'multi_file_processing',
      type: 'task_request',
    };

    console.log('发送的消息数据:', messageData);
    console.log('文件数量:', files.length);

    const jsonStr = JSON.stringify(messageData);
    console.log('Valid JSON sent:', jsonStr);

    wsSend(jsonStr);

    // 发送成功后清除文件并关闭附件栏
    uploadedFiles.value = [];
    items.value = [];
    isOpen.value = false; // 关闭附件栏
  };

  onMounted(async () => {
    console.log('AI聊天组件开始初始化...');

    try {
      // 获取公司列表
      await fetchCompanyNames();
      console.log('公司列表获取完成:', companyList.value);
    } catch (error) {
      console.error('获取公司列表失败:', error);
      initializationError.value = '获取公司列表失败';
    }

    try {
      // 打开WebSocket连接
      wsOpen();
      console.log('WebSocket连接已启动');
    } catch (error) {
      console.error('WebSocket连接失败:', error);
      initializationError.value = 'WebSocket连接失败';
    }

    // 监听消息列表变化，自动滚动到底部
    watch(
      messages,
      () => {
        // 使用nextTick确保DOM已更新
        nextTick(() => {
          scrollToBottom();
        });
      },
      { deep: true },
    );

    // 初始化完成
    isInitializing.value = false;
    console.log('AI聊天组件初始化完成');
  });

  onUnmounted(() => {
    wsClose();
  });

  const loading = computed(() => aiChatStore.isProcessing);
  const messages = computed(() => {
    const msgs = aiChatStore.currentMessages;
    console.log('当前消息列表:', msgs);
    return msgs;
  });

  // 计算是否可以发送消息（有文本内容或有附件时可以发送）
  const canSendMessage = computed(() => {
    const hasContent = inputValue.value.trim().length > 0;
    const hasFiles = uploadedFiles.value.length > 0;
    return hasContent || hasFiles;
  });

  // 自定义发送按钮
  const customActions = (oriNode: any, { components }: any) => {
    const { SendButton } = components;

    return h(
      SendButton,
      {
        disabled: !canSendMessage.value,
        loading: loading.value,
        onClick: () => {
          if (canSendMessage.value) {
            handleSubmit(inputValue.value);
          }
        },
      },
      '发送',
    );
  };

  type SuggestionItems = Exclude<SuggestionProps['items'], () => void>;

  const suggestions: SuggestionItems = [
    { label: '生成凭证', value: '生成凭证' },
  ];

  const promptTemplates: PromptsProps['items'] = [
    {
      icon: h(SmileOutlined, { style: { color: '#52C41A' } }),
      key: '1',
      label: '生成凭证',
    },
  ];

  const inputRef = ref();
  const inputValue = ref('');

  const handleSubmit = async (content: string) => {
    // 检查是否有内容或文件
    const hasContent = content.trim().length > 0;
    const hasFiles = uploadedFiles.value.length > 0;

    if (!hasContent && !hasFiles) {
      message.warning('请输入内容或上传文件');
      return;
    }

    if (loading.value) return;

    // 清空输入框
    inputValue.value = '';

    // 添加用户消息
    let messageContent = content.trim();
    if (hasFiles) {
      const fileNames = uploadedFiles.value
        .map((file) => file.original_filename)
        .join(', ');
      if (hasContent) {
        messageContent += `\n\n📎 附件: ${fileNames}`;
      } else {
        messageContent = `📎 发送了 ${uploadedFiles.value.length} 个文件: ${fileNames}`;
      }
    }

    if (hasContent || hasFiles) {
      aiChatStore.addMessage({
        content: messageContent,
        status: 'success',
        type: 'user',
      });
    }

    // 滚动到底部
    setTimeout(() => scrollToBottom(), 100);

    try {
      // 发送 WebSocket 消息
      sendMessageWithFiles(content);
    } catch (error) {
      console.error('Error sending message:', error);
      message.error('发送消息失败，请重试');
    }
  };

  const isOpen = ref(false);

  // 详情模态框状态
  const detailModalVisible = ref(false);
  const selectedMessage = ref<ChatMessage | null>(null);

  const showMessageDetail = (message: ChatMessage) => {
    selectedMessage.value = message;
    detailModalVisible.value = true;
  };

  // 创建紧凑的消息卡片组件
  const createCompactCard = (
    title: string,
    content: any,
    type: 'info' | 'success' | 'warning' = 'info',
  ) => {
    const colors = {
      info: { bg: '#e6f7ff', border: '#91d5ff', text: '#1890ff' },
      success: { bg: '#f6ffed', border: '#b7eb8f', text: '#52c41a' },
      warning: { bg: '#fffbe6', border: '#ffe58f', text: '#faad14' },
    };

    return h(
      'div',
      {
        style: {
          background: colors[type].bg,
          border: `1px solid ${colors[type].border}`,
          borderRadius: '6px',
          fontSize: '12px',
          marginBottom: '6px',
          padding: '8px 12px',
        },
      },
      [
        h(
          'div',
          {
            style: {
              color: colors[type].text,
              fontSize: '12px',
              fontWeight: '600',
              marginBottom: '4px',
            },
          },
          title,
        ),
        h(
          'div',
          {
            style: {
              color: '#666',
              fontSize: '11px',
              lineHeight: '1.4',
            },
          },
          content,
        ),
      ],
    );
  };

  // 创建关键信息标签
  const createInfoTag = (
    label: string,
    value: number | string,
    type: 'primary' | 'success' | 'warning' = 'primary',
  ) => {
    const colors = {
      primary: { bg: '#e6f7ff', text: '#1890ff' },
      success: { bg: '#f6ffed', text: '#52c41a' },
      warning: { bg: '#fffbe6', text: '#faad14' },
    };

    return h(
      'span',
      {
        style: {
          background: colors[type].bg,
          borderRadius: '3px',
          color: colors[type].text,
          display: 'inline-block',
          fontSize: '10px',
          fontWeight: '500',
          marginBottom: '4px',
          marginRight: '6px',
          padding: '2px 6px',
        },
      },
      `${label}: ${value}`,
    );
  };

  const bubbleItems = computed(() =>
    messages.value.map((msg) => {
      const lastMessage = messages.value[messages.value.length - 1];
      const isLoading =
        (msg.type === 'ai' ||
          msg.type === 'bank_receipt_extracted' ||
          msg.type === 'salary_data_extracted') &&
        lastMessage &&
        lastMessage.id === msg.id &&
        loading.value;

      // 银行回单类型 - 紧凑展示
      if (msg.type === 'bank_receipt_extracted' && msg.extraData?.data) {
        const data = msg.extraData.data;
        return {
          content: h('div', { class: 'compact-message' }, [
            h('div', { class: 'message-title' }, msg.content),
            h('div', { class: 'info-tags' }, [
              createInfoTag('金额', `¥${data.amount}`, 'success'),
              createInfoTag('账户', data.account_name || '未知', 'primary'),
              createInfoTag('时间', data.transaction_time || '未知', 'primary'),
            ]),
            data.summary && createCompactCard('交易摘要', data.summary, 'info'),
            h('div', { class: 'message-actions' }, [
              h(
                Button,
                {
                  onClick: () => showMessageDetail(msg),
                  size: 'small',
                  type: 'link',
                },
                '查看详情',
              ),
            ]),
          ]),
          key: msg.id,
          loading: isLoading,
          meta: formatTime(msg.timestamp),
          role: 'ai',
          shape: 'corner' as const,
          status: msg.status,
          type: msg.type,
        };
      }

      // 工资单数据类型 - 紧凑展示
      if (msg.type === 'salary_data_extracted' && msg.extraData?.data) {
        const data = msg.extraData.data;
        return {
          content: h('div', { class: 'compact-message' }, [
            h('div', { class: 'message-title' }, msg.content),
            h('div', { class: 'info-tags' }, [
              createInfoTag('员工数', data.employee_count || 0, 'primary'),
              createInfoTag(
                '工资总额',
                `¥${(data.total_salary || 0).toLocaleString()}`,
                'success',
              ),
              createInfoTag(
                '实发总额',
                `¥${(data.total_actual_salary || 0).toLocaleString()}`,
                'warning',
              ),
            ]),
            data.company_name &&
              createCompactCard('公司信息', data.company_name, 'info'),
            h('div', { class: 'message-actions' }, [
              h(
                Button,
                {
                  onClick: () => showMessageDetail(msg),
                  size: 'small',
                  type: 'link',
                },
                '查看详情',
              ),
            ]),
          ]),
          key: msg.id,
          loading: isLoading,
          meta: formatTime(msg.timestamp),
          role: 'ai',
          shape: 'corner' as const,
          status: msg.status,
          type: msg.type,
        };
      }

      // 凭证生成完成类型 - 紧凑展示
      if (msg.type === 'voucher_generation_completed' && msg.extraData?.data) {
        const data = msg.extraData.data;
        const summary = data.conversion_summary;
        return {
          content: h('div', { class: 'compact-message' }, [
            h('div', { class: 'message-title' }, msg.content),
            summary &&
              h('div', { class: 'info-tags' }, [
                createInfoTag(
                  '凭证数',
                  summary.total_vouchers_generated || 0,
                  'success',
                ),
                createInfoTag(
                  '成功率',
                  `${summary.success_rate || 0}%`,
                  summary.success_rate >= 90 ? 'success' : 'warning',
                ),
                createInfoTag(
                  '借方总额',
                  summary.total_debit_amount || 0,
                  'primary',
                ),
                createInfoTag(
                  '贷方总额',
                  summary.total_credit_amount || 0,
                  'primary',
                ),
              ]),
            summary &&
              createCompactCard(
                '处理结果',
                `成功处理 ${summary.successful_conversions || 0} 条记录，生成 ${summary.total_vouchers_generated || 0} 张凭证`,
                'success',
              ),
            h('div', { class: 'message-actions' }, [
              h(
                Button,
                {
                  onClick: () => showMessageDetail(msg),
                  size: 'small',
                  type: 'link',
                },
                '查看详情',
              ),
            ]),
          ]),
          key: msg.id,
          loading: isLoading,
          meta: formatTime(msg.timestamp),
          role: 'ai',
          shape: 'corner' as const,
          status: msg.status,
          type: msg.type,
        };
      }

      // 任务状态类型 - 改进的进度展示
      if (msg.type === 'task_status' && msg.thoughtChainItems) {
        const totalSteps = msg.thoughtChainItems.length;
        // 检查最后一个步骤的状态来判断任务是否完成
        const lastStep = msg.thoughtChainItems[totalSteps - 1];
        const isTaskCompleted =
          lastStep?.data?.status === 'completed' ||
          lastStep?.status === 'success';
        // 如果任务已完成，所有步骤都算成功；否则除了最后一个步骤，前面的都算成功
        const completedSteps = isTaskCompleted
          ? totalSteps
          : totalSteps > 0
            ? totalSteps - 1
            : 0;
        const progressPercent =
          totalSteps > 0 ? Math.round((completedSteps / totalSteps) * 100) : 0;

        return {
          content: h('div', { class: 'compact-message task-progress' }, [
            h('div', { class: 'message-title' }, msg.content),

            // 进度条
            h('div', { class: 'progress-bar-container' }, [
              h('div', { class: 'progress-bar-bg' }, [
                h('div', {
                  class: 'progress-bar-fill',
                  style: { width: `${progressPercent}%` },
                }),
              ]),
              h(
                'div',
                { class: 'progress-text' },
                `${completedSteps}/${totalSteps} (${progressPercent}%)`,
              ),
            ]),

            // 步骤列表 - 显示最近的3-5个步骤
            h(
              'div',
              { class: 'progress-steps' },
              msg.thoughtChainItems.slice(-4).map((item, index, array) => {
                // 修正状态逻辑：根据任务完成状态和步骤位置确定状态
                const isLastStep = index === array.length - 1;
                const stepStatus = isTaskCompleted
                  ? 'success'
                  : isLastStep
                    ? 'pending'
                    : 'success';

                return h(
                  'div',
                  {
                    class: ['progress-step', stepStatus],
                    key: `step-${index}`,
                  },
                  [
                    h(
                      'div',
                      { class: 'step-icon' },
                      stepStatus === 'success' ? '✓' : '⋯',
                    ),
                    h('div', { class: 'step-content' }, [
                      h('div', { class: 'step-title' }, item.title),
                      item.description &&
                        h(
                          'div',
                          { class: 'step-description' },
                          item.description,
                        ),
                    ]),
                  ],
                );
              }),
            ),

            // 如果有更多步骤，显示展开按钮
            totalSteps > 4 &&
              h('div', { class: 'message-actions' }, [
                h(
                  Button,
                  {
                    onClick: () => showMessageDetail(msg),
                    size: 'small',
                    type: 'link',
                  },
                  `查看全部 ${totalSteps} 个步骤`,
                ),
              ]),
          ]),
          key: msg.id,
          loading: isLoading,
          meta: formatTime(msg.timestamp),
          role: 'ai',
          shape: 'corner' as const,
          status: msg.status,
          type: msg.type,
        };
      }

      // 普通消息
      return {
        content: h('div', { class: 'simple-message' }, msg.content),
        key: msg.id,
        loading: isLoading,
        meta: formatTime(msg.timestamp),
        role: msg.type,
        shape: 'corner' as const,
        status: msg.status,
        type: msg.type,
        typing: msg.type === 'ai' ? { interval: 50, step: 2 } : undefined,
      };
    }),
  );

  const roles: BubbleListProps['roles'] = {
    ai: {
      avatar: { icon: h(RobotOutlined), style: { background: '#1890ff' } },
      placement: 'start',
      typing: true,
    },
    file: {
      avatar: { icon: h(UserOutlined), style: { visibility: 'hidden' } },
      messageRender: (items: any) =>
        h(
          Flex,
          { gap: 'middle', vertical: true },
          items.map((item: any) =>
            h(Attachments.FileCard, { item, key: item.uid }),
          ),
        ),
      placement: 'start',
      variant: 'borderless',
    },
    image: {
      avatar: { icon: h(UserOutlined), style: { visibility: 'hidden' } },
      messageRender: (items: any) =>
        h(
          'div',
          { style: { display: 'flex', flexDirection: 'column', gap: '8px' } },
          items.map((item: any) =>
            h('img', {
              alt: item.name,
              key: item.uid,
              src: item.url,
              style: { borderRadius: '4px', maxWidth: '300px' },
            }),
          ),
        ),
      placement: 'start',
      variant: 'borderless',
    },

    suggestion: {
      avatar: { icon: h(UserOutlined), style: { visibility: 'hidden' } },
      messageRender: (items) =>
        h(Prompts, { vertical: true, items: items as any }),
      placement: 'start',
      variant: 'borderless',
    },
    user: {
      avatar: { icon: h(UserOutlined), style: { background: '#52c41a' } },
      placement: 'end',
    },
  };

  const formatTime = (date: Date) => {
    return new Intl.DateTimeFormat('zh-CN', {
      hour: '2-digit',
      minute: '2-digit',
    }).format(date);
  };

  // 滚动到底部的函数
  const scrollToBottom = () => {
    const messagesContainer = document.querySelector('.messages-container');
    if (messagesContainer) {
      messagesContainer.scrollTop = messagesContainer.scrollHeight;
    }
  };

  // const open = ref(false);

  const attachmentsRef = ref<InstanceType<typeof Attachments> | null>(null);
  const items = ref<UploadFile[]>([]);

  const getDropContainer = () => {
    // 确保返回一个稳定的拖拽容器
    const container = inputRef.value?.nativeElement;
    console.log('getDropContainer called, container:', container);
    return container || document.body;
  };

  const handleFileUpload = (firstFile: File, fileList: FileList) => {
    console.log(
      'handleFileUpload called with files:',
      [...fileList].map((f) => f.name),
    );
    console.log('First file:', firstFile.name);
    console.log('Total files:', fileList.length);

    try {
      // 确保附件面板是打开的
      isOpen.value = true;

      // 逐个上传文件，添加延迟以避免并发问题
      [...fileList].forEach((file, index) => {
        setTimeout(() => {
          console.log(
            `Uploading file ${index + 1}/${fileList.length}:`,
            file.name,
          );
          if (attachmentsRef.value) {
            attachmentsRef.value.upload(file);
          } else {
            console.error(
              'attachmentsRef is null when trying to upload file:',
              file.name,
            );
          }
        }, index * 100); // 每个文件间隔100ms上传
      });
    } catch (error) {
      console.error('Error in handleFileUpload:', error);
      message.error('文件上传处理失败');
    }
  };

  const placeholder = (type: string) => {
    return type === 'drop'
      ? {
          title: 'Drop file here',
        }
      : {
          description: 'Click or drag files to this area to upload',
          icon: h(CloudUploadOutlined),
          title: '上传文件',
        };
  };

  const handlePromptSelect = (info: {
    data: PromptsProps['items'][number];
  }) => {
    const label = info.data.label;
    if (typeof label === 'string') {
      handleSubmit(label);
    }
  };

  const handleSuggestionSelect = (itemVal: string) => {
    updateValue(`[${itemVal}]:`);
  };

  const handleSenderChange = (
    nextVal: string,
    { onTrigger }: { onTrigger?: (show?: boolean) => void } = {},
  ) => {
    if (nextVal === '/') {
      onTrigger?.();
    } else if (!nextVal) {
      onTrigger?.(false);
    }
    updateValue(nextVal);
  };

  const updateValue = (nextVal: string) => {
    inputValue.value = nextVal;
  };

  // 重新加载页面
  const reloadPage = () => {
    window.location.reload();
  };

  // 手动重连WebSocket
  const reconnectWebSocket = () => {
    console.log('手动重连WebSocket...');
    message.loading('正在重连...', 1);
    wsClose();
    setTimeout(() => {
      wsOpen();
    }, 1000);
  };
</script>

<template>
  <div class="chat-container">
    <div class="chat-card">
      <div class="chat-header">
        <div class="header-content">
          <!-- 第一行：标题和连接状态 -->
          <div class="header-title">
            <img
              src="https://cdn-icons-png.flaticon.com/128/8943/8943377.png"
              class="title-icon"
            />
            <span class="title-text">精算家AI会计助手</span>
            <!-- 连接状态指示器 -->
            <div class="connection-status" :class="wsStatus.toLowerCase()">
              <div class="status-dot"></div>
              <span class="status-text">
                {{
                  wsStatus === 'OPEN'
                    ? '已连接'
                    : wsStatus === 'CONNECTING'
                      ? '连接中'
                      : '未连接'
                }}
              </span>
              <!-- 重连按钮，只在断开连接时显示 -->
              <Button
                v-if="wsStatus === 'CLOSED'"
                type="text"
                size="small"
                @click="reconnectWebSocket"
                class="reconnect-btn"
                title="点击重连"
              >
                🔄
              </Button>
            </div>
          </div>

          <!-- 第二行：选择器 -->
          <div class="header-controls">
            <div class="control-group">
              <label class="control-label">公司:</label>
              <Tooltip
                :title="companySelectionStore.selectedCompany || '请选择公司'"
              >
                <Select
                  v-model:value="companySelectionStore.selectedCompany"
                  style="width: 160px !important"
                  placeholder="请选择公司"
                  @change="handleCompanyChange"
                  show-search
                  :filter-option="
                    (input, option) =>
                      String(option?.children || '')
                        .toLowerCase()
                        .includes(input.toLowerCase())
                  "
                >
                  <Select.Option
                    v-for="company in companyList"
                    :key="company.id"
                    :value="company.name"
                    :title="company.name"
                  >
                    {{ company.name }}
                  </Select.Option>
                </Select>
              </Tooltip>
            </div>

            <div class="control-group">
              <label class="control-label">月份:</label>
              <Select
                v-model:value="selectedMonth"
                placeholder="选择月份"
                style="width: 120px !important"
              >
                <Select.Option
                  v-for="month in monthOptions"
                  :key="month.value"
                  :value="month.value"
                >
                  {{ month.label }}
                </Select.Option>
              </Select>
            </div>
          </div>
        </div>
      </div>

      <div class="chat-layout">
        <div class="chat-main">
          <div class="messages-container">
            <!-- 初始化加载状态 -->
            <div v-if="isInitializing" class="loading-container">
              <div class="loading-content">
                <div class="loading-spinner"></div>
                <p class="loading-text">正在初始化AI助手...</p>
              </div>
            </div>

            <!-- 初始化错误状态 -->
            <div v-else-if="initializationError" class="error-container">
              <div class="error-content">
                <div class="error-icon">⚠️</div>
                <p class="error-text">{{ initializationError }}</p>
                <Button type="primary" @click="reloadPage">重新加载</Button>
              </div>
            </div>

            <!-- 正常状态 -->
            <div v-else-if="messages.length === 0" class="welcome-container">
              <Welcome
                :class-names="{
                  title: 'welcome-title',
                  description: 'welcome-description',
                }"
                icon="https://cdn-icons-png.flaticon.com/128/8943/8943377.png"
              >
                <template #title>精算家AI会计助手</template>
                <template #description>
                  <div class="welcome-desc-content">
                    <p class="main-desc">
                      专业的AI助手，帮您快速生成准确的会计凭证
                    </p>
                    <p class="sub-desc">上传财务文件，即可自动识别并生成凭证</p>
                  </div>
                </template>
              </Welcome>
            </div>
            <Bubble.List
              v-else
              :items="bubbleItems"
              :roles="roles"
              :style="{ padding: '8px' }"
              class="bubble-list"
            />
          </div>

          <div class="input-container">
            <div style="padding-bottom: 6px">
              <Prompts
                :items="promptTemplates"
                :on-item-click="handlePromptSelect"
                style="transform: scale(0.8); transform-origin: left center"
              />
            </div>
            <div class="sender-container">
              <Suggestion :items="suggestions" @select="handleSuggestionSelect">
                <template #default="{ onTrigger }: any">
                  <Sender
                    v-model:value="inputValue"
                    ref="inputRef"
                    placeholder="输入消息，按 Enter 发送..."
                    :loading="loading"
                    :actions="customActions"
                    @submit="handleSubmit"
                    @paste-file="handleFileUpload"
                    @change="(val) => handleSenderChange(val, { onTrigger })"
                  >
                    <template #prefix>
                      <Button
                        type="text"
                        :icon="h(LinkOutlined)"
                        @click="
                          () => {
                            isOpen = !isOpen;
                          }
                        "
                      />
                    </template>
                    <template #header>
                      <Sender.Header
                        title="附件"
                        :styles="{
                          content: {
                            padding: 0,
                          },
                        }"
                        :open="isOpen"
                        :on-open-change="(v) => (isOpen = v)"
                        force-render
                      >
                        <Attachments
                          ref="attachmentsRef"
                          :before-upload="() => false"
                          :items="items"
                          :on-change="fileChange"
                          :placeholder="placeholder"
                          :get-drop-container="getDropContainer"
                        />
                      </Sender.Header>
                    </template>
                  </Sender>
                </template>
              </Suggestion>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 详情模态框 -->
    <MessageDetailModal
      v-model:visible="detailModalVisible"
      :message="selectedMessage"
    />
  </div>
</template>

<style scoped lang="scss">
  // 将 keyframes 移动到最顶部
  @keyframes pulse {
    0%,
    100% {
      opacity: 1;
      transform: scale(1);
    }

    50% {
      opacity: 0.7;
      transform: scale(1.2);
    }
  }

  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }

    100% {
      transform: rotate(360deg);
    }
  }

  .chat-container {
    height: 100%;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  }

  .chat-card {
    display: flex;
    flex-direction: column;
    height: 100%;
    overflow: hidden;
    border-radius: 12px;
    box-shadow: 0 8px 32px rgb(0 0 0 / 10%);

    :deep(.ant-card-body) {
      height: 100%;
      padding: 0;
    }
  }

  .chat-header {
    width: 100% !important;
    padding: 10px 20px !important;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    border-bottom: none !important;
    box-shadow: 0 2px 8px rgb(0 0 0 / 10%) !important;

    .header-content {
      display: flex !important;
      flex-direction: column !important;
      gap: 8px !important;
      align-items: center !important;
      justify-content: center !important;
      width: 100% !important;
    }

    .header-title {
      display: flex !important;
      gap: 10px !important;
      align-items: center !important;
      justify-content: center !important;

      .title-icon {
        width: 24px !important;
        height: 24px !important;
        border-radius: 6px !important;
      }

      .title-text {
        font-size: 15px !important;
        font-weight: 600 !important;
        color: white !important;
        text-shadow: 0 1px 2px rgb(0 0 0 / 10%) !important;
      }

      .connection-status {
        display: flex !important;
        gap: 4px !important;
        align-items: center !important;
        padding: 2px 8px !important;
        background: rgb(255 255 255 / 20%) !important;
        backdrop-filter: blur(10px) !important;
        border-radius: 12px !important;

        .status-dot {
          width: 6px !important;
          height: 6px !important;
          border-radius: 50% !important;
          transition: all 0.3s ease !important;
        }

        .status-text {
          font-size: 10px !important;
          font-weight: 500 !important;
          color: white !important;
          text-shadow: 0 1px 2px rgb(0 0 0 / 10%) !important;
        }

        &.open {
          .status-dot {
            background: #52c41a !important;
            box-shadow: 0 0 8px rgb(82 196 26 / 50%) !important;
          }
        }

        &.connecting {
          .status-dot {
            background: #faad14 !important;
            box-shadow: 0 0 8px rgb(250 173 20 / 50%) !important;
            animation: pulse 1.5s infinite !important;
          }
        }

        &.closed {
          .status-dot {
            background: #ff4d4f !important;
            box-shadow: 0 0 8px rgb(255 77 79 / 50%) !important;
          }
        }

        .reconnect-btn {
          min-width: 16px !important;
          height: 16px !important;
          padding: 0 4px !important;
          font-size: 10px !important;
          color: white !important;
          background: transparent !important;
          border: none !important;
          transition: all 0.2s ease !important;

          &:hover {
            background: rgb(255 255 255 / 20%) !important;
            transform: rotate(180deg) !important;
          }
        }
      }
    }

    .header-controls {
      display: flex !important;
      gap: 20px !important;
      align-items: center !important;
      justify-content: center !important;
    }

    .control-group {
      display: flex !important;
      gap: 6px !important;
      align-items: center !important;

      .control-label {
        font-size: 12px !important;
        font-weight: 500 !important;
        color: white !important;
        text-shadow: 0 1px 2px rgb(0 0 0 / 10%) !important;
        white-space: nowrap !important;
      }
    }

    .ant-select {
      width: 160px !important;
      margin-right: 0 !important;
      border-radius: 8px !important;
      box-shadow: 0 2px 8px rgb(255 255 255 / 20%) !important;

      :deep(.ant-select-selector) {
        background: rgb(255 255 255 / 90%) !important;
        backdrop-filter: blur(10px) !important;
        border: 1px solid rgb(255 255 255 / 30%) !important;
      }
    }

    .ant-picker {
      width: 120px !important;
      border-radius: 8px !important;
      box-shadow: 0 2px 8px rgb(255 255 255 / 20%) !important;

      :deep(.ant-picker-input) {
        background: rgb(255 255 255 / 90%) !important;
        backdrop-filter: blur(10px) !important;
        border: 1px solid rgb(255 255 255 / 30%) !important;
      }
    }
  }

  .chat-layout {
    display: flex;
    flex: 1;
    height: calc(100% - 68px);
    overflow: hidden;
  }

  .chat-main {
    display: flex;
    flex: 1;
    flex-direction: column;
    height: 100%;
    overflow: hidden;
  }

  .messages-container {
    flex: 1;
    padding: 8px;
    overflow-y: auto;
    background: rgb(255 255 255 / 80%);
    backdrop-filter: blur(10px);

    &::-webkit-scrollbar {
      width: 6px;
      height: 6px;
    }

    &::-webkit-scrollbar-thumb {
      background: linear-gradient(135deg, #667eea, #764ba2);
      border-radius: 6px;
      transition: all 0.3s ease;

      &:hover {
        background: linear-gradient(135deg, #5a67d8, #6b46c1);
      }
    }

    &::-webkit-scrollbar-track {
      background: rgb(255 255 255 / 10%);
      border-radius: 6px;
    }
  }

  .input-container {
    padding: 8px 16px;
    background: rgb(255 255 255 / 95%);
    backdrop-filter: blur(20px);
    border-top: 1px solid rgb(255 255 255 / 20%);
    box-shadow: 0 -4px 16px rgb(0 0 0 / 5%);

    // 缩小Prompts组件
    :deep(.ant-prompts) {
      .ant-prompts-item {
        min-height: 28px !important;
        padding: 4px 10px !important;
        margin: 2px !important;
        font-size: 11px !important;
        background: linear-gradient(
          135deg,
          #f8fafc 0%,
          #e2e8f0 100%
        ) !important;
        border: 1px solid rgb(255 255 255 / 60%) !important;
        border-radius: 6px !important;
        transition: all 0.2s ease !important;

        &:hover {
          color: white !important;
          background: linear-gradient(
            135deg,
            #667eea 0%,
            #764ba2 100%
          ) !important;
          box-shadow: 0 4px 12px rgb(102 126 234 / 30%) !important;
          transform: translateY(-1px) !important;
        }
      }

      .ant-prompts-item-icon {
        font-size: 12px !important;
      }

      .ant-prompts-item-label {
        font-size: 11px !important;
        font-weight: 500 !important;
      }
    }
  }

  .bubble-list {
    :deep(.ant-bubble) {
      max-width: 85%;
      margin: 6px 0;
      transition: all 0.3s ease;

      &:hover {
        box-shadow: 0 6px 16px rgb(0 0 0 / 8%);
        transform: translateY(-1px);
      }

      &[data-type='ai'],
      &[data-type='bank_receipt_extracted'],
      &[data-type='salary_data_extracted'],
      &[data-type='voucher_generation_completed'] {
        margin-right: auto;
        background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        border: 1px solid rgb(255 255 255 / 60%);
        border-radius: 16px 16px 16px 4px;
        box-shadow: 0 2px 8px rgb(0 0 0 / 6%);

        .ant-bubble-content {
          padding: 10px 12px !important;
          font-size: 12px !important;
          line-height: 1.4 !important;
          color: #333;

          // 紧凑消息样式
          .compact-message {
            .message-title {
              margin-bottom: 8px !important;
              font-size: 13px !important;
              font-weight: 600 !important;
              line-height: 1.3 !important;
              color: #1890ff !important;
            }

            .info-tags {
              margin-bottom: 8px !important;
              line-height: 1.2 !important;
            }

            .progress-info {
              font-size: 11px !important;
              line-height: 1.3 !important;
              color: #666 !important;
            }

            .message-actions {
              margin-top: 8px !important;
              text-align: right !important;

              .ant-btn-link {
                height: auto !important;
                padding: 0 4px !important;
                font-size: 11px !important;
                color: #1890ff !important;

                &:hover {
                  color: #40a9ff !important;
                }
              }
            }

            // 任务进度样式
            &.task-progress {
              .progress-bar-container {
                margin: 8px 0 !important;

                .progress-bar-bg {
                  width: 100% !important;
                  height: 6px !important;
                  overflow: hidden !important;
                  background: #f0f0f0 !important;
                  border-radius: 3px !important;

                  .progress-bar-fill {
                    height: 100% !important;
                    background: linear-gradient(
                      90deg,
                      #1890ff 0%,
                      #40a9ff 100%
                    ) !important;
                    border-radius: 3px !important;
                    transition: width 0.3s ease !important;
                  }
                }

                .progress-text {
                  margin-top: 4px !important;
                  font-size: 10px !important;
                  color: #666 !important;
                  text-align: center !important;
                }
              }

              .progress-steps {
                margin-top: 8px !important;

                .progress-step {
                  display: flex !important;
                  align-items: flex-start !important;
                  padding: 4px 0 !important;
                  margin-bottom: 6px !important;

                  &:last-child {
                    margin-bottom: 0 !important;
                  }

                  .step-icon {
                    flex-shrink: 0 !important;
                    width: 16px !important;
                    height: 16px !important;
                    margin-right: 8px !important;
                    font-size: 10px !important;
                    line-height: 16px !important;
                    text-align: center !important;
                    border-radius: 50% !important;
                  }

                  &.success .step-icon {
                    color: #52c41a !important;
                    background: #f6ffed !important;
                    border: 1px solid #b7eb8f !important;
                  }

                  &.error .step-icon {
                    color: #ff4d4f !important;
                    background: #fff2f0 !important;
                    border: 1px solid #ffccc7 !important;
                  }

                  &.pending .step-icon {
                    color: #999 !important;
                    background: #f0f0f0 !important;
                    border: 1px solid #d9d9d9 !important;
                  }

                  .step-content {
                    flex: 1 !important;

                    .step-title {
                      font-size: 11px !important;
                      font-weight: 500 !important;
                      line-height: 1.3 !important;
                      color: #333 !important;
                    }

                    .step-description {
                      margin-top: 2px !important;
                      font-size: 10px !important;
                      line-height: 1.2 !important;
                      color: #666 !important;
                    }
                  }
                }
              }
            }
          }

          // 简单消息样式
          .simple-message {
            font-size: 12px !important;
            line-height: 1.4 !important;
            color: #333 !important;
          }
        }
      }

      &[data-type='user'] {
        margin-left: auto;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: 1px solid rgb(255 255 255 / 20%);
        border-radius: 20px 20px 6px;
        box-shadow: 0 4px 16px rgb(102 126 234 / 30%);

        .ant-bubble-content {
          font-size: 12px !important;
          font-weight: 500;
          line-height: 1.5 !important;
          color: #fff;
        }

        .ant-bubble-meta {
          color: rgb(255 255 255 / 80%);
        }
      }

      &[data-status='loading'] {
        opacity: 0.8;
      }

      &[data-status='error'] {
        border: 1px solid #ff4d4f;
      }

      .ant-bubble-meta {
        margin-top: 3px;
        font-size: 10px;
        opacity: 0.7;
      }

      .ant-bubble-actions {
        margin-top: 6px;
        opacity: 0;
        transition: opacity 0.2s;

        .bubble-action {
          margin-right: 12px;
          color: #666;
          cursor: pointer;

          &:hover {
            color: #1677ff;
          }

          &:last-child {
            margin-right: 0;
          }
        }
      }

      &:hover .ant-bubble-actions {
        opacity: 1;
      }
    }

    :deep(.ant-welcome) {
      padding: 20px !important;
      text-align: center !important;
      background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%) !important;
      border: 1px solid rgb(255 255 255 / 60%) !important;
      border-radius: 12px !important;
      box-shadow: 0 4px 16px rgb(0 0 0 / 8%) !important;
    }

    :deep(.ant-welcome-icon) {
      display: flex !important;
      align-items: center !important;
      justify-content: center !important;
      width: 40px !important;
      height: 40px !important;
      margin-bottom: 12px !important;
    }
  }

  .welcome-container {
    padding: 16px !important;
  }

  .loading-container,
  .error-container {
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    height: 100% !important;
    padding: 32px !important;
  }

  .loading-content,
  .error-content {
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
    justify-content: center !important;
    text-align: center !important;
  }

  .loading-spinner {
    width: 32px !important;
    height: 32px !important;
    margin-bottom: 16px !important;
    border: 3px solid #f3f3f3 !important;
    border-top: 3px solid #667eea !important;
    border-radius: 50% !important;
    animation: spin 1s linear infinite !important;
  }

  .loading-text {
    margin: 0 !important;
    font-size: 14px !important;
    color: #666 !important;
  }

  .error-icon {
    margin-bottom: 16px !important;
    font-size: 32px !important;
  }

  .error-text {
    margin: 0 0 16px !important;
    font-size: 14px !important;
    color: #ff4d4f !important;
  }

  .welcome-title {
    margin-bottom: 10px !important;
    font-size: 18px !important;
    font-weight: 600 !important;
    color: #2d3748 !important;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    background-clip: text !important;
    -webkit-text-fill-color: transparent !important;
  }

  .welcome-description {
    font-size: 12px !important;
    line-height: 1.5 !important;
    color: #4a5568 !important;
  }

  .welcome-desc-content {
    .main-desc {
      margin-bottom: 6px !important;
      font-size: 13px !important;
      font-weight: 500 !important;
      line-height: 1.4 !important;
      color: #4a5568 !important;
    }

    .sub-desc {
      margin-bottom: 0 !important;
      font-size: 12px !important;
      line-height: 1.4 !important;
      color: #718096 !important;
    }
  }
</style>

<!-- 全局样式覆盖 -->
<style>
  /* 紧凑消息样式 */
  .ant-bubble-content .compact-message {
    font-size: 12px !important;
    line-height: 1.4 !important;
  }

  .ant-bubble-content .simple-message {
    font-size: 12px !important;
    line-height: 1.4 !important;
  }

  /* 减小气泡间距 */
  .ant-bubble {
    margin: 4px 0 !important;
  }
</style>
