export default defineEventHandler(async (event) => {
  const body = await readBody(event);
  const { message, files } = body;

  // 模拟AI处理延迟
  await new Promise(resolve => setTimeout(resolve, 1000));

  // 模拟不同类型的响应
  let response = '';
  let action = null;

  if (message.includes('文件') && files && files.length > 0) {
    // 文件上传处理
    response = `我已经收到了您上传的 ${files.length} 个文件。正在分析文件内容...`;
    
    // 模拟文件分析结果
    setTimeout(() => {
      action = {
        type: 'navigate',
        path: '/demo/table',
        params: {
          data: generateMockTableData(files[0])
        }
      };
    }, 2000);
  } else if (message.includes('表格') || message.includes('数据分析')) {
    response = '我正在为您生成表格数据，请稍候...';
    action = {
      type: 'navigate',
      path: '/demo/table',
      params: {
        data: generateMockTableData()
      }
    };
  } else if (message.includes('用户管理')) {
    response = '正在为您打开用户管理页面...';
    action = {
      type: 'navigate',
      path: '/system/user'
    };
  } else if (message.includes('系统配置')) {
    response = '正在为您打开系统配置页面...';
    action = {
      type: 'navigate',
      path: '/system/config'
    };
  } else {
    response = `我收到了您的消息："${message}"。这是一个模拟的AI回复。我可以帮您处理文件分析、数据展示、系统导航等任务。`;
  }

  return {
    success: true,
    data: {
      response,
      action,
      timestamp: new Date().toISOString()
    }
  };
});

function generateMockTableData(file?: any) {
  const fileName = file?.name || '示例数据';
  
  return {
    title: `${fileName} - 数据分析结果`,
    columns: [
      { key: 'id', title: 'ID', dataIndex: 'id' },
      { key: 'name', title: '名称', dataIndex: 'name' },
      { key: 'type', title: '类型', dataIndex: 'type' },
      { key: 'value', title: '数值', dataIndex: 'value' },
      { key: 'status', title: '状态', dataIndex: 'status' },
      { key: 'createTime', title: '创建时间', dataIndex: 'createTime' }
    ],
    data: Array.from({ length: 20 }, (_, index) => ({
      id: index + 1,
      name: `数据项 ${index + 1}`,
      type: ['类型A', '类型B', '类型C'][index % 3],
      value: Math.floor(Math.random() * 1000),
      status: ['正常', '异常', '待处理'][index % 3],
      createTime: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toLocaleDateString()
    }))
  };
}
